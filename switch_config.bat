@echo off
chcp 65001 >nul
title تبديل إعدادات الخادم - نظام الشؤون القانونية

echo.
echo ========================================
echo    تبديل إعدادات خادم النظام
echo ========================================
echo.

echo اختر نوع الإعداد المطلوب:
echo.
echo 1. الوصول المحلي فقط (أمان عالي)
echo 2. الوصول من الشبكة المحلية (متوازن)
echo 3. إعدادات الإنتاج (HTTPS)
echo 4. عرض الإعدادات الحالية
echo 5. إنشاء نسخة احتياطية من الإعدادات
echo 0. الخروج
echo.

set /p choice="أدخل اختيارك (0-5): "

if "%choice%"=="1" goto local_only
if "%choice%"=="2" goto local_network
if "%choice%"=="3" goto production
if "%choice%"=="4" goto show_current
if "%choice%"=="5" goto backup
if "%choice%"=="0" goto exit
goto invalid

:local_only
echo.
echo 📋 تطبيق إعدادات الوصول المحلي فقط...
copy "network_configs\local_only.json" "server_config.json" >nul
echo ✅ تم تطبيق الإعدادات بنجاح
echo 🔒 الوصول محدود للجهاز المحلي فقط
echo 🌐 الرابط: http://localhost:5000
goto end

:local_network
echo.
echo 📋 تطبيق إعدادات الشبكة المحلية...
copy "network_configs\local_network.json" "server_config.json" >nul
echo ✅ تم تطبيق الإعدادات بنجاح
echo 🌐 يمكن الوصول من جميع أجهزة الشبكة المحلية
echo 💡 تأكد من فتح المنفذ 5000 في جدار الحماية
goto end

:production
echo.
echo 📋 تطبيق إعدادات الإنتاج...
copy "network_configs\production.json" "server_config.json" >nul
echo ✅ تم تطبيق الإعدادات بنجاح
echo ⚠️  تحتاج لتكوين شهادات SSL قبل التشغيل
echo 🔒 سيعمل النظام على المنفذ 443 مع HTTPS
echo 📝 راجع README_SERVER_SETUP.md لتفاصيل إعداد SSL
goto end

:show_current
echo.
echo 📖 الإعدادات الحالية:
echo =====================================
if exist "server_config.json" (
    type "server_config.json"
) else (
    echo ❌ ملف الإعدادات غير موجود
)
goto end

:backup
echo.
echo 💾 إنشاء نسخة احتياطية...
if exist "server_config.json" (
    for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c-%%a-%%b)
    for /f "tokens=1-2 delims=/:" %%a in ('time /t') do (set mytime=%%a%%b)
    copy "server_config.json" "server_config_backup_%mydate%_%mytime%.json" >nul
    echo ✅ تم إنشاء نسخة احتياطية: server_config_backup_%mydate%_%mytime%.json
) else (
    echo ❌ لا يوجد ملف إعدادات للنسخ الاحتياطي
)
goto end

:invalid
echo.
echo ❌ اختيار غير صحيح
goto end

:end
echo.
echo 💡 لتطبيق الإعدادات الجديدة، أعد تشغيل الخادم
echo 🔧 أو استخدم خيار "إعادة تشغيل الخادم" من واجهة النظام
echo.
pause
goto exit

:exit
echo.
echo 👋 شكراً لاستخدام نظام الشؤون القانونية
exit /b 0
