/* ملف CSS خاص بالطباعة */
@media print {
    /* إخفاء العناصر غير المطلوبة في الطباعة */
    .no-print,
    .btn,
    .btn-group,
    .btn-toolbar,
    .navbar,
    .sidebar,
    .breadcrumb,
    .pagination,
    .modal,
    .alert,
    .dropdown,
    .form-control,
    .form-select,
    .form-check,
    .card-header .btn,
    .card-footer,
    .fixed-top,
    .fixed-bottom,
    .sticky-top {
        display: none !important;
    }

    /* إعدادات الصفحة */
    @page {
        size: A4;
        margin: 1cm;
    }

    body {
        font-family: 'Arial', 'Tahoma', sans-serif;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: white;
        margin: 0;
        padding: 0;
    }

    /* رأس التقرير */
    .print-header {
        display: block !important;
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #333;
        page-break-inside: avoid;
    }

    .company-logo {
        max-height: 80px;
        max-width: 200px;
        margin-bottom: 10px;
    }

    .company-name {
        font-size: 24pt;
        font-weight: bold;
        color: #2c3e50;
        margin: 10px 0;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .company-subtitle {
        font-size: 14pt;
        color: #7f8c8d;
        margin-bottom: 5px;
    }

    .report-title {
        font-size: 20pt;
        font-weight: bold;
        color: #34495e;
        margin: 20px 0 10px 0;
        text-align: center;
    }

    .report-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 15px 0;
        padding: 10px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        font-size: 11pt;
    }

    .print-date {
        font-weight: bold;
        color: #495057;
    }

    .report-period {
        font-weight: bold;
        color: #495057;
    }

    /* تنسيق الجداول */
    .table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        font-size: 11pt;
    }

    .table th,
    .table td {
        border: 1px solid #333;
        padding: 8px;
        text-align: right;
        vertical-align: middle;
    }

    .table th {
        background-color: #34495e;
        color: white;
        font-weight: bold;
        text-align: center;
    }

    .table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    .table tbody tr:nth-child(odd) {
        background-color: white;
    }

    /* تنسيق البطاقات */
    .card {
        border: 1px solid #333;
        margin-bottom: 20px;
        page-break-inside: avoid;
        box-shadow: none;
    }

    .card-header {
        background-color: #34495e !important;
        color: white !important;
        font-weight: bold;
        padding: 10px;
        border-bottom: 1px solid #333;
    }

    .card-body {
        padding: 15px;
        background-color: white;
    }

    /* تنسيق الإحصائيات */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
    }

    .stat-item {
        border: 1px solid #333;
        padding: 15px;
        text-align: center;
        background-color: #f8f9fa;
    }

    .stat-value {
        font-size: 18pt;
        font-weight: bold;
        color: #2c3e50;
        display: block;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 12pt;
        color: #7f8c8d;
    }

    /* تنسيق المعلومات الشخصية */
    .employee-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin: 20px 0;
    }

    .info-section {
        border: 1px solid #333;
        padding: 15px;
    }

    .info-title {
        font-size: 14pt;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
        border-bottom: 1px solid #bdc3c7;
        padding-bottom: 5px;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        margin: 8px 0;
        padding: 5px 0;
        border-bottom: 1px dotted #bdc3c7;
    }

    .info-label {
        font-weight: bold;
        color: #34495e;
    }

    .info-value {
        color: #2c3e50;
    }

    /* تنسيق التوقيعات */
    .signatures {
        margin-top: 50px;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 30px;
        page-break-inside: avoid;
    }

    .signature-box {
        text-align: center;
        border-top: 1px solid #333;
        padding-top: 10px;
        margin-top: 40px;
    }

    .signature-title {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .signature-name {
        color: #7f8c8d;
        font-size: 10pt;
    }

    /* تنسيق ذيل الصفحة */
    .print-footer {
        position: fixed;
        bottom: 1cm;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 10pt;
        color: #7f8c8d;
        border-top: 1px solid #bdc3c7;
        padding-top: 10px;
    }

    /* فواصل الصفحات */
    .page-break {
        page-break-before: always;
    }

    .page-break-inside-avoid {
        page-break-inside: avoid;
    }

    /* تنسيق العناوين */
    h1, h2, h3, h4, h5, h6 {
        color: #2c3e50;
        margin: 15px 0 10px 0;
        page-break-after: avoid;
    }

    /* تنسيق النصوص */
    .text-center {
        text-align: center;
    }

    .text-right {
        text-align: right;
    }

    .text-left {
        text-align: left;
    }

    .font-weight-bold {
        font-weight: bold;
    }

    /* تنسيق الشارات */
    .badge {
        background-color: #6c757d !important;
        color: white !important;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 10pt;
    }

    .badge.bg-success {
        background-color: #28a745 !important;
    }

    .badge.bg-danger {
        background-color: #dc3545 !important;
    }

    .badge.bg-warning {
        background-color: #ffc107 !important;
        color: #000 !important;
    }

    .badge.bg-info {
        background-color: #17a2b8 !important;
    }

    .badge.bg-primary {
        background-color: #007bff !important;
    }

    /* تنسيق خاص للأرقام */
    .number {
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }

    /* تنسيق التواريخ */
    .date {
        font-family: 'Courier New', monospace;
    }

    /* إخفاء عناصر التفاعل */
    a[href]:after {
        content: none !important;
    }

    /* تحسين جودة الطباعة */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* رأس التقرير - مخفي افتراضياً ويظهر فقط في الطباعة */
.print-header {
    display: none;
}

@media print {
    .print-header {
        display: block !important;
    }
}
