from django.shortcuts import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, send_file
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from datetime import datetime, date
import os
import uuid
from decimal import Decimal
import json
from permissions_manager import permission_required, any_permission_required

bp = Blueprint('contracts', __name__, url_prefix='/contracts')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from django.shortcuts import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        return {}

def get_db():
    """الحصول على كائن قاعدة البيانات"""
    try:
        from django.shortcuts import current_app
        return current_app.extensions['sqlalchemy'].db
    except (RuntimeError, KeyError):
        return None

@bp.route('/')
@login_required
@permission_required('view_contracts')
def index():
    """صفحة قائمة العقود"""
    try:
        models = get_models()
        Contract = models.get('Contract')
        Client = models.get('Client')
        Lawyer = models.get('Lawyer')
        
        if not all([Contract, Client, Lawyer]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))
        
        # معاملات البحث والفلترة
        search = request.GET.get('search', '')
        status_filter = request.GET.get('status', '')
        contract_type_filter = request.GET.get('contract_type', '')
        client_filter = request.GET.get('client', '')
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')
        
        # بناء الاستعلام
        query = Contract.query
        
        # البحث النصي
        if search:
            from sqlalchemy import or_
            query = query.filter(
                or_(
                    Contract.title.contains(search),
                    Contract.contract_number.contains(search),
                    Contract.client_name.contains(search),
                    Contract.description.contains(search)
                )
            )
        
        # فلترة حسب الحالة
        if status_filter:
            query = query.filter(Contract.status == status_filter)
        
        # فلترة حسب نوع العقد
        if contract_type_filter:
            query = query.filter(Contract.contract_type == contract_type_filter)
        
        # فلترة حسب العميل
        if client_filter:
            query = query.filter(Contract.client_id == client_filter)
        
        # فلترة حسب التاريخ
        if date_from:
            query = query.filter(Contract.start_date >= datetime.strptime(date_from, '%Y-%m-%d').date())
        if date_to:
            query = query.filter(Contract.start_date <= datetime.strptime(date_to, '%Y-%m-%d').date())
        
        # ترتيب النتائج
        contracts = query.order_by(Contract.created_at.desc()).all()
        
        # جلب قوائم للفلاتر
        clients = Client.query.filter_by(is_active=True).all()
        db = get_db()
        if db:
            contract_types = db.query(Contract.contract_type).distinct().all()
            contract_types = [ct[0] for ct in contract_types if ct[0]]
        else:
            contract_types = []
        
        # إحصائيات سريعة
        stats = {
            'total': Contract.query.count(),
            'active': Contract.query.filter_by(status='active').count(),
            'draft': Contract.query.filter_by(status='draft').count(),
            'expired': Contract.query.filter(Contract.end_date < date.today()).count()
        }
        
        return render('contracts/index.html',
                             contracts=contracts,
                             clients=clients,
                             contract_types=contract_types,
                             stats=stats,
                             search=search,
                             status_filter=status_filter,
                             contract_type_filter=contract_type_filter,
                             client_filter=client_filter,
                             date_from=date_from,
                             date_to=date_to)
                             
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('create_contracts')
def add():
    """إضافة عقد جديد"""
    try:
        models = get_models()
        Contract = models.get('Contract')
        Client = models.get('Client')
        Lawyer = models.get('Lawyer')
        db = get_db()
        
        if not all([Contract, Client, Lawyer]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('contracts.index'))
        
        if request.method == 'POST':
            # جلب البيانات من النموذج
            title = request.POST.get('title')
            title_en = request.POST.get('title_en', '')
            client_id = request.POST.get('client_id')
            contract_type = request.POST.get('contract_type')
            contract_type_en = request.POST.get('contract_type_en', '')
            description = request.POST.get('description', '')
            description_en = request.POST.get('description_en', '')
            terms_and_conditions = request.POST.get('terms_and_conditions', '')
            terms_and_conditions_en = request.POST.get('terms_and_conditions_en', '')
            start_date = request.POST.get('start_date')
            end_date = request.POST.get('end_date', '')
            duration_months = request.POST.get('duration_months', '')
            total_amount = request.POST.get('total_amount')
            currency = request.POST.get('currency', 'SAR')
            payment_terms = request.POST.get('payment_terms', '')
            payment_terms_en = request.POST.get('payment_terms_en', '')
            status = request.POST.get('status', 'draft')
            priority = request.POST.get('priority', 'medium')
            assigned_lawyer_id = request.POST.get('assigned_lawyer_id', '')
            notes = request.POST.get('notes', '')
            notes_en = request.POST.get('notes_en', '')
            tags = request.POST.get('tags', '')
            
            # التحقق من البيانات المطلوبة
            if not all([title, client_id, contract_type, start_date, total_amount]):
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return redirect(url_for('contracts.add'))
            
            # جلب بيانات العميل
            client = Client.query.get(client_id)
            if not client:
                flash('العميل المحدد غير موجود', 'error')
                return redirect(url_for('contracts.add'))
            
            # إنشاء رقم العقد
            contract_number = f"CON-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
            
            # معالجة رفع الملف
            pdf_file_path = None
            original_filename = None
            file_size = None
            
            if 'pdf_file' in request.files:
                file = request.files['pdf_file']
                if file and file.filename:
                    if file.filename.lower().endswith('.pdf'):
                        # إنشاء مجلد العقود إذا لم يكن موجوداً
                        contracts_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'contracts')
                        os.makedirs(contracts_dir, exist_ok=True)
                        
                        # حفظ الملف
                        filename = secure_filename(f"{contract_number}_{file.filename}")
                        file_path = os.path.join(contracts_dir, filename)
                        file.save(file_path)
                        
                        pdf_file_path = file_path
                        original_filename = file.filename
                        file_size = os.path.getsize(file_path)
                    else:
                        flash('يجب أن يكون الملف من نوع PDF', 'error')
                        return redirect(url_for('contracts.add'))
            
            # إنشاء العقد الجديد
            new_contract = Contract(
                contract_number=contract_number,
                title=title,
                title_en=title_en,
                client_id=client_id,
                client_name=client.full_name,
                client_email=client.email,
                client_phone=client.phone,
                client_address=client.address,
                contract_type=contract_type,
                contract_type_en=contract_type_en,
                description=description,
                description_en=description_en,
                terms_and_conditions=terms_and_conditions,
                terms_and_conditions_en=terms_and_conditions_en,
                start_date=datetime.strptime(start_date, '%Y-%m-%d').date(),
                end_date=datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None,
                duration_months=int(duration_months) if duration_months else None,
                total_amount=Decimal(total_amount),
                currency=currency,
                payment_terms=payment_terms,
                payment_terms_en=payment_terms_en,
                status=status,
                priority=priority,
                assigned_lawyer_id=int(assigned_lawyer_id) if assigned_lawyer_id else None,
                notes=notes,
                notes_en=notes_en,
                tags=tags,
                pdf_file_path=pdf_file_path,
                original_filename=original_filename,
                file_size=file_size,
                created_by_id=current_user.id
            )
            
            db.add(new_contract)
            db.save()
            
            flash('تم إضافة العقد بنجاح', 'success')
            return redirect(url_for('contracts.view', id=new_contract.id))
        
        # GET request - عرض النموذج
        clients = Client.query.filter_by(is_active=True).all()
        lawyers = Lawyer.query.filter_by(is_active=True).all()
        
        return render('contracts/add.html',
                             clients=clients,
                             lawyers=lawyers)
                             
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('contracts.index'))

@bp.route('/view/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل العقد"""
    try:
        models = get_models()
        Contract = models.get('Contract')
        
        if not Contract:
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('contracts.index'))
        
        contract = Contract.query.get_or_404(id)
        
        return render('contracts/view.html', contract=contract)
        
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('contracts.index'))

@bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@permission_required('edit_contracts')
def edit(id):
    """تعديل العقد"""
    try:
        models = get_models()
        Contract = models.get('Contract')
        Client = models.get('Client')
        Lawyer = models.get('Lawyer')
        db = get_db()
        
        if not all([Contract, Client, Lawyer]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('contracts.index'))
        
        contract = Contract.query.get_or_404(id)
        
        if request.method == 'POST':
            # تحديث البيانات
            contract.title = request.POST.get('title')
            contract.title_en = request.POST.get('title_en', '')
            contract.contract_type = request.POST.get('contract_type')
            contract.contract_type_en = request.POST.get('contract_type_en', '')
            contract.description = request.POST.get('description', '')
            contract.description_en = request.POST.get('description_en', '')
            contract.terms_and_conditions = request.POST.get('terms_and_conditions', '')
            contract.terms_and_conditions_en = request.POST.get('terms_and_conditions_en', '')
            
            start_date = request.POST.get('start_date')
            if start_date:
                contract.start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            
            end_date = request.POST.get('end_date', '')
            contract.end_date = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None
            
            duration_months = request.POST.get('duration_months', '')
            contract.duration_months = int(duration_months) if duration_months else None
            
            total_amount = request.POST.get('total_amount')
            if total_amount:
                contract.total_amount = Decimal(total_amount)
            
            contract.currency = request.POST.get('currency', 'SAR')
            contract.payment_terms = request.POST.get('payment_terms', '')
            contract.payment_terms_en = request.POST.get('payment_terms_en', '')
            contract.status = request.POST.get('status', 'draft')
            contract.priority = request.POST.get('priority', 'medium')
            
            assigned_lawyer_id = request.POST.get('assigned_lawyer_id', '')
            contract.assigned_lawyer_id = int(assigned_lawyer_id) if assigned_lawyer_id else None
            
            contract.notes = request.POST.get('notes', '')
            contract.notes_en = request.POST.get('notes_en', '')
            contract.tags = request.POST.get('tags', '')
            
            # معالجة رفع ملف جديد
            if 'pdf_file' in request.files:
                file = request.files['pdf_file']
                if file and file.filename:
                    if file.filename.lower().endswith('.pdf'):
                        # حذف الملف القديم إذا كان موجوداً
                        if contract.pdf_file_path and os.path.exists(contract.pdf_file_path):
                            os.remove(contract.pdf_file_path)
                        
                        # حفظ الملف الجديد
                        contracts_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'contracts')
                        os.makedirs(contracts_dir, exist_ok=True)
                        
                        filename = secure_filename(f"{contract.contract_number}_{file.filename}")
                        file_path = os.path.join(contracts_dir, filename)
                        file.save(file_path)
                        
                        contract.pdf_file_path = file_path
                        contract.original_filename = file.filename
                        contract.file_size = os.path.getsize(file_path)
                    else:
                        flash('يجب أن يكون الملف من نوع PDF', 'error')
                        return redirect(url_for('contracts.edit', id=id))
            
            contract.updated_at = datetime.utcnow()
            db.save()
            
            flash('تم تحديث العقد بنجاح', 'success')
            return redirect(url_for('contracts.view', id=id))
        
        # GET request - عرض نموذج التعديل
        clients = Client.query.filter_by(is_active=True).all()
        lawyers = Lawyer.query.filter_by(is_active=True).all()
        
        return render('contracts/edit.html',
                             contract=contract,
                             clients=clients,
                             lawyers=lawyers)
                             
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('contracts.index'))

@bp.route('/download/<int:id>')
@login_required
@permission_required('download_contracts')
def download(id):
    """تحميل ملف PDF للعقد"""
    try:
        models = get_models()
        Contract = models.get('Contract')
        
        if not Contract:
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('contracts.index'))
        
        contract = Contract.query.get_or_404(id)
        
        if not contract.pdf_file_path or not os.path.exists(contract.pdf_file_path):
            flash('ملف العقد غير موجود', 'error')
            return redirect(url_for('contracts.view', id=id))
        
        return send_file(
            contract.pdf_file_path,
            as_attachment=True,
            download_name=contract.original_filename or f"contract_{contract.contract_number}.pdf"
        )
        
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('contracts.index'))

@bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    """حذف العقد"""
    try:
        models = get_models()
        Contract = models.get('Contract')
        db = get_db()

        if not Contract:
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        contract = Contract.query.get_or_404(id)

        # حذف الملف المرفق إذا كان موجوداً
        if contract.pdf_file_path and os.path.exists(contract.pdf_file_path):
            os.remove(contract.pdf_file_path)

        db.delete(contract)
        db.save()

        return JsonResponse({'success': True, 'message': 'تم حذف العقد بنجاح'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/api/stats')
@login_required
def api_stats():
    """API للحصول على إحصائيات العقود"""
    try:
        models = get_models()
        Contract = models.get('Contract')
        db = get_db()

        if not Contract:
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        # إحصائيات عامة
        total_contracts = Contract.query.count()
        active_contracts = Contract.query.filter_by(status='active').count()
        draft_contracts = Contract.query.filter_by(status='draft').count()
        completed_contracts = Contract.query.filter_by(status='completed').count()
        expired_contracts = Contract.query.filter(Contract.end_date < date.today()).count()

        # إحصائيات مالية
        total_value = db.query(db.func.sum(Contract.total_amount)).filter_by(status='active').scalar() or 0

        # العقود المنتهية قريباً (خلال 30 يوم)
        from datetime import timedelta
        upcoming_expiry = Contract.query.filter(
            Contract.end_date.between(date.today(), date.today() + timedelta(days=30))
        ).count()

        return JsonResponse({
            'success': True,
            'stats': {
                'total': total_contracts,
                'active': active_contracts,
                'draft': draft_contracts,
                'completed': completed_contracts,
                'expired': expired_contracts,
                'upcoming_expiry': upcoming_expiry,
                'total_value': float(total_value)
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/reports')
@login_required
def reports():
    """صفحة تقارير العقود"""
    try:
        models = get_models()
        Contract = models.get('Contract')
        db = get_db()

        if not Contract:
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('contracts.index'))

        # إحصائيات للتقارير
        stats = {
            'total': Contract.query.count(),
            'by_status': {},
            'by_type': {},
            'financial': {}
        }

        # إحصائيات حسب الحالة
        statuses = db.query(Contract.status, db.func.count(Contract.id)).group_by(Contract.status).all()
        for status, count in statuses:
            stats['by_status'][status] = count

        # إحصائيات حسب النوع
        types = db.query(Contract.contract_type, db.func.count(Contract.id)).group_by(Contract.contract_type).all()
        for contract_type, count in types:
            if contract_type:
                stats['by_type'][contract_type] = count

        # إحصائيات مالية
        total_value = db.query(db.func.sum(Contract.total_amount)).scalar() or 0
        active_value = db.query(db.func.sum(Contract.total_amount)).filter_by(status='active').scalar() or 0

        stats['financial'] = {
            'total_value': float(total_value),
            'active_value': float(active_value),
            'average_value': float(total_value / max(stats['total'], 1))
        }

        return render('contracts/reports.html', stats=stats)

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('contracts.index'))

@bp.route('/language/<lang>')
@login_required
def set_language(lang):
    """تغيير لغة العرض"""
    try:
        # حفظ اللغة في الجلسة
        from django.shortcuts import session
        if lang in ['ar', 'en']:
            request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.request.session['language'] = lang
            flash('تم تغيير اللغة بنجاح' if lang == 'ar' else 'Language changed successfully', 'success')
        else:
            flash('لغة غير مدعومة', 'error')

        return redirect(request.referrer or url_for('contracts.index'))

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('contracts.index'))
