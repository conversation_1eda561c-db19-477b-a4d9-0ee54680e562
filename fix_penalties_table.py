#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جدول الجزاءات - إضافة العمود المفقود
Fix Penalties Table - Add Missing Column
"""

import sqlite3
import os
import sys

def fix_penalties_table():
    """إصلاح جدول الجزاءات بإضافة العمود المفقود"""

    # البحث عن قاعدة البيانات في المواقع المحتملة
    possible_paths = [
        'legal_system.db',
        'instance/legal_system.db',
        os.path.join('instance', 'legal_system.db')
    ]

    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            print(f"🔍 تم العثور على قاعدة البيانات: {db_path}")
            break

    if not db_path:
        print("❌ لم يتم العثور على أي ملف قاعدة بيانات")
        return False

    return fix_penalties_table_for_path(db_path)

def fix_penalties_table_for_path(db_path):
    """إصلاح جدول الجزاءات لمسار محدد"""
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 فحص هيكل جدول الجزاءات...")
        
        # التحقق من وجود جدول الجزاءات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='penalties'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ جدول الجزاءات غير موجود")
            return False
        
        # فحص أعمدة الجدول الحالية
        cursor.execute("PRAGMA table_info(penalties)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        print(f"📋 الأعمدة الموجودة: {', '.join(column_names)}")
        
        # التحقق من وجود عمود category
        if 'category' in column_names:
            print("✅ عمود category موجود بالفعل")
            return True
        
        print("🔄 إضافة عمود category...")
        
        # إضافة عمود category
        cursor.execute("""
            ALTER TABLE penalties 
            ADD COLUMN category VARCHAR(50) NOT NULL DEFAULT 'general'
        """)
        
        print("✅ تم إضافة عمود category بنجاح")
        
        # التحقق من النتيجة
        cursor.execute("PRAGMA table_info(penalties)")
        new_columns = cursor.fetchall()
        new_column_names = [column[1] for column in new_columns]
        
        print(f"📋 الأعمدة بعد التحديث: {', '.join(new_column_names)}")
        
        # حفظ التغييرات
        conn.save()
        
        # عرض عدد السجلات
        cursor.execute("SELECT COUNT(*) FROM penalties")
        count = cursor.fetchone()[0]
        print(f"📊 عدد سجلات الجزاءات: {count}")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 بدء إصلاح جدول الجزاءات...")
    print("=" * 50)

    # فحص جميع قواعد البيانات المحتملة
    possible_paths = [
        'legal_system.db',
        'instance/legal_system.db'
    ]

    success_count = 0
    for db_path in possible_paths:
        if os.path.exists(db_path):
            print(f"\n🔍 فحص قاعدة البيانات: {db_path}")
            if fix_penalties_table_for_path(db_path):
                success_count += 1

    print("=" * 50)
    if success_count > 0:
        print(f"✅ تم إصلاح {success_count} قاعدة بيانات بنجاح")
        return True
    else:
        print("❌ فشل في إصلاح جدول الجزاءات")
        return False

def fix_penalties_table_for_path(db_path):
    """إصلاح جدول الجزاءات لمسار محدد"""

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
