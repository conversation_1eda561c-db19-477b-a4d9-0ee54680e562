#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تثبيت نظام معالجة الأخطاء التلقائي
Auto Error Fixer Installation Script
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    requirements = ["watchdog"]
    
    print("📦 تثبيت المتطلبات...")
    
    for package in requirements:
        try:
            print(f"تثبيت {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"❌ فشل في تثبيت {package}")
            return False
    
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "error_fix_backups",
        "logs"
    ]
    
    print("📁 إنشاء المجلدات...")
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(exist_ok=True)
        print(f"✅ تم إنشاء مجلد: {directory}")
    
    return True

def check_project_structure():
    """فحص بنية المشروع"""
    expected_dirs = ["templates", "routes", "static"]
    found_dirs = []
    
    print("🔍 فحص بنية المشروع...")
    
    for directory in expected_dirs:
        if Path(directory).exists():
            found_dirs.append(directory)
            print(f"✅ موجود: {directory}")
        else:
            print(f"⚠️ غير موجود: {directory}")
    
    if not found_dirs:
        print("⚠️ لم يتم العثور على مجلدات المشروع المتوقعة")
        print("تأكد من تشغيل السكريبت في مجلد المشروع الصحيح")
    
    return len(found_dirs) > 0

def create_startup_script():
    """إنشاء سكريبت بدء التشغيل"""
    startup_content = '''@echo off
chcp 65001 >nul
title نظام معالجة الأخطاء التلقائي

echo.
echo ========================================
echo      نظام معالجة الأخطاء التلقائي
echo ========================================
echo.

echo 1. تشغيل الإصلاح الشامل
echo 2. تشغيل المراقب المستمر
echo 3. إصلاح سريع للقوالب
echo 4. خروج
echo.

set /p choice="اختر (1-4): "

if "%choice%"=="1" (
    echo تشغيل الإصلاح الشامل...
    python auto_error_fixer.py
) else if "%choice%"=="2" (
    echo تشغيل المراقب المستمر...
    python error_monitor.py
) else if "%choice%"=="3" (
    echo إصلاح سريع للقوالب...
    python fix_errors.py --templates
) else if "%choice%"=="4" (
    exit
) else (
    echo اختيار غير صحيح
)

pause
'''
    
    with open("start_error_fixer.bat", "w", encoding="utf-8") as f:
        f.write(startup_content)
    
    print("✅ تم إنشاء سكريبت بدء التشغيل: start_error_fixer.bat")

def test_installation():
    """اختبار التثبيت"""
    print("🧪 اختبار التثبيت...")
    
    # فحص الملفات المطلوبة
    required_files = [
        "auto_error_fixer.py",
        "fix_errors.py", 
        "error_monitor.py",
        "error_fixer_config.json"
    ]
    
    missing_files = []
    for file_name in required_files:
        if not Path(file_name).exists():
            missing_files.append(file_name)
        else:
            print(f"✅ موجود: {file_name}")
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {missing_files}")
        return False
    
    # اختبار استيراد المكتبات
    try:
        import watchdog
        print("✅ مكتبة watchdog متاحة")
    except ImportError:
        print("❌ مكتبة watchdog غير متاحة")
        return False
    
    # اختبار تشغيل سريع
    try:
        from auto_error_fixer import AutoErrorFixer
        fixer = AutoErrorFixer()
        print("✅ يمكن تشغيل معالج الأخطاء")
    except Exception as e:
        print(f"❌ خطأ في تشغيل معالج الأخطاء: {e}")
        return False
    
    return True

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    instructions = """
🎉 تم تثبيت نظام معالجة الأخطاء التلقائي بنجاح!

📖 طرق الاستخدام:

1️⃣ الإصلاح الشامل:
   python auto_error_fixer.py

2️⃣ الإصلاح السريع:
   python fix_errors.py --templates    # القوالب فقط
   python fix_errors.py --python       # Python فقط
   python fix_errors.py --javascript   # JavaScript فقط

3️⃣ المراقبة المستمرة:
   python error_monitor.py

4️⃣ استخدام ملف Batch:
   start_error_fixer.bat

📁 الملفات المهمة:
- error_fix_backups/     : النسخ الاحتياطية
- auto_error_fixer.log   : سجل العمليات
- error_fixer_config.json : الإعدادات

📚 للمزيد من المعلومات:
   اقرأ ملف ERROR_FIXER_README.md

🚀 ابدأ الآن بتشغيل:
   python auto_error_fixer.py
"""
    print(instructions)

def main():
    """الوظيفة الرئيسية"""
    print("🔧 تثبيت نظام معالجة الأخطاء التلقائي")
    print("=" * 50)
    
    # فحص إصدار Python
    if not check_python_version():
        return False
    
    # فحص بنية المشروع
    if not check_project_structure():
        response = input("هل تريد المتابعة رغم ذلك؟ (y/n): ")
        if response.lower() not in ['y', 'yes', 'نعم']:
            return False
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        return False
    
    # إنشاء المجلدات
    if not create_directories():
        print("❌ فشل في إنشاء المجلدات")
        return False
    
    # إنشاء سكريبت بدء التشغيل
    create_startup_script()
    
    # اختبار التثبيت
    if not test_installation():
        print("❌ فشل في اختبار التثبيت")
        return False
    
    # عرض تعليمات الاستخدام
    show_usage_instructions()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ تم التثبيت بنجاح!")
        else:
            print("\n❌ فشل في التثبيت")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء التثبيت")
    except Exception as e:
        print(f"\n❌ خطأ في التثبيت: {e}")
        sys.exit(1)
