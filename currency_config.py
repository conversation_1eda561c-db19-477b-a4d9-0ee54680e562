# إعدادات العملات المدعومة في النظام

CURRENCIES = {
    'SAR': {
        'name': 'ريال سعودي',
        'symbol': 'ر.س',
        'code': 'SAR',
        'decimal_places': 2,
        'format': '{amount} {symbol}',
        'country': 'المملكة العربية السعودية'
    },
    'AED': {
        'name': 'درهم إماراتي',
        'symbol': 'د.إ',
        'code': 'AED',
        'decimal_places': 2,
        'format': '{amount} {symbol}',
        'country': 'دولة الإمارات العربية المتحدة'
    },
    'KWD': {
        'name': 'دينار كويتي',
        'symbol': 'د.ك',
        'code': 'KWD',
        'decimal_places': 3,
        'format': '{amount} {symbol}',
        'country': 'دولة الكويت'
    },
    'EGP': {
        'name': 'جنيه مصري',
        'symbol': 'ج.م',
        'code': 'EGP',
        'decimal_places': 2,
        'format': '{amount} {symbol}',
        'country': 'جمهورية مصر العربية'
    },
    'USD': {
        'name': 'دولار أمريكي',
        'symbol': '$',
        'code': 'USD',
        'decimal_places': 2,
        'format': '{symbol}{amount}',
        'country': 'الولايات المتحدة الأمريكية'
    },
    'EUR': {
        'name': 'يورو',
        'symbol': '€',
        'code': 'EUR',
        'decimal_places': 2,
        'format': '{symbol}{amount}',
        'country': 'الاتحاد الأوروبي'
    }
}

def format_currency(amount, currency_code='SAR'):
    """تنسيق المبلغ حسب العملة المحددة"""
    if currency_code not in CURRENCIES:
        currency_code = 'SAR'
    
    currency = CURRENCIES[currency_code]
    
    # تنسيق المبلغ حسب عدد الخانات العشرية
    formatted_amount = f"{amount:.{currency['decimal_places']}f}"
    
    # تطبيق تنسيق العملة
    return currency['format'].format(
        amount=formatted_amount,
        symbol=currency['symbol']
    )

def get_currency_symbol(currency_code='SAR'):
    """الحصول على رمز العملة"""
    if currency_code not in CURRENCIES:
        currency_code = 'SAR'
    return CURRENCIES[currency_code]['symbol']

def get_currency_name(currency_code='SAR'):
    """الحصول على اسم العملة"""
    if currency_code not in CURRENCIES:
        currency_code = 'SAR'
    return CURRENCIES[currency_code]['name']

def get_supported_currencies():
    """الحصول على قائمة العملات المدعومة"""
    return CURRENCIES

def validate_currency(currency_code):
    """التحقق من صحة رمز العملة"""
    return currency_code in CURRENCIES
