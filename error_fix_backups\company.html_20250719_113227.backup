{% extends "base.html" %} {% block title %}إعدادات الشركة - نظام الشؤون
القانونية{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2">
    <i class="fas fa-building me-2"></i>
    إعدادات الشركة
  </h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
      <a
        href="{{ {% url 'settings.index' %} }}"
        class="btn btn-outline-secondary"
      >
        <i class="fas fa-arrow-right me-1"></i>
        العودة للإعدادات
      </a>
      <button onclick="previewReport()" class="btn btn-info">
        <i class="fas fa-eye me-1"></i>
        معاينة التقرير
      </button>
    </div>
  </div>
</div>

<form id="companyForm" enctype="multipart/form-data">
  <div class="row">
    <div class="col-md-8">
      <!-- معلومات الشركة الأساسية -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            معلومات الشركة الأساسية
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="company_name" class="form-label"
                  >اسم الشركة <span class="text-danger">*</span></label
                >
                <input
                  type="text"
                  class="form-control"
                  id="company_name"
                  name="company_name"
                  value="{{ company_name }}"
                  required
                />
                <div class="form-text">الاسم الذي سيظهر في جميع التقارير</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="company_subtitle" class="form-label"
                  >وصف الشركة</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="company_subtitle"
                  name="company_subtitle"
                  value="{{ company_subtitle }}"
                />
                <div class="form-text">الوصف الذي يظهر تحت اسم الشركة</div>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="company_address" class="form-label">عنوان الشركة</label>
            <textarea
              class="form-control"
              id="company_address"
              name="company_address"
              rows="3"
            >
{{ company_address }}</textarea
            >
            <div class="form-text">العنوان الكامل للشركة</div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label for="company_phone" class="form-label">رقم الهاتف</label>
                <input
                  type="tel"
                  class="form-control"
                  id="company_phone"
                  name="company_phone"
                  value="{{ company_phone }}"
                  placeholder="+966501234567"
                />
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="company_email" class="form-label"
                  >البريد الإلكتروني</label
                >
                <input
                  type="email"
                  class="form-control"
                  id="company_email"
                  name="company_email"
                  value="{{ company_email }}"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="company_website" class="form-label"
                  >الموقع الإلكتروني</label
                >
                <input
                  type="url"
                  class="form-control"
                  id="company_website"
                  name="company_website"
                  value="{{ company_website }}"
                  placeholder="https://www.company.com"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- شعار الشركة -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-image me-2"></i>
            شعار الشركة
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="logo_file" class="form-label">رفع شعار جديد</label>
                <input
                  type="file"
                  class="form-control"
                  id="logo_file"
                  name="logo_file"
                  accept=".png,.jpg,.jpeg,.svg,.gif"
                />
                <div class="form-text">
                  الأنواع المدعومة: PNG, JPG, SVG, GIF (حد أقصى 5 ميجابايت)
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">الشعار الحالي</label>
                <div class="border rounded p-3 text-center">
                  <img
                    src="{{ url_for('static', filename=logo_path) }}"
                    alt="شعار الشركة"
                    class="img-fluid"
                    style="max-height: 100px; max-width: 200px"
                    onerror="this.src='/static/images/logo.svg'"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="alert alert-info">
            <h6 class="alert-heading">
              <i class="fas fa-lightbulb me-1"></i>
              نصائح للشعار:
            </h6>
            <ul class="mb-0">
              <li>استخدم صورة عالية الجودة للحصول على أفضل نتيجة في الطباعة</li>
              <li>الأبعاد المثلى: 200x80 بكسل أو نسبة مماثلة</li>
              <li>تجنب الألوان الفاتحة جداً التي قد لا تظهر في الطباعة</li>
              <li>ملفات SVG توفر أفضل جودة في جميع الأحجام</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <!-- معاينة التقرير -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-eye me-2"></i>
            معاينة التقرير
          </h5>
        </div>
        <div class="card-body">
          <div
            id="reportPreview"
            class="border rounded p-3"
            style="background-color: #f8f9fa"
          >
            <div class="text-center">
              <img
                src="{{ url_for('static', filename=logo_path) }}"
                alt="شعار الشركة"
                style="max-height: 60px; max-width: 150px"
                onerror="this.src='/static/images/logo.svg'"
              />
              <h6 class="mt-2 mb-1" id="previewCompanyName">
                {{ company_name }}
              </h6>
              <small class="text-muted" id="previewCompanySubtitle"
                >{{ company_subtitle }}</small
              >
              <hr />
              <small class="text-muted">عينة من رأس التقرير</small>
            </div>
          </div>
          <div class="mt-3">
            <small class="text-muted">
              هذه معاينة مبسطة لكيفية ظهور الشعار واسم الشركة في التقارير
            </small>
          </div>
        </div>
      </div>

      <!-- أزرار الحفظ -->
      <div class="card">
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save me-1"></i>
              حفظ الإعدادات
            </button>
            <a
              href="{{ {% url 'settings.index' %} }}"
              class="btn btn-outline-secondary"
            >
              <i class="fas fa-times me-1"></i>
              إلغاء
            </a>
            <button
              type="button"
              onclick="resetSettings()"
              class="btn btn-outline-danger"
            >
              <i class="fas fa-undo me-1"></i>
              إعادة تعيين
            </button>
          </div>
        </div>
      </div>

      <!-- معلومات إضافية -->
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="fas fa-info me-2"></i>
            معلومات مهمة
          </h6>
        </div>
        <div class="card-body">
          <small class="text-muted">
            <ul class="mb-0">
              <li>ستظهر هذه المعلومات في جميع التقارير المطبوعة</li>
              <li>يمكن تغيير الشعار في أي وقت</li>
              <li>التغييرات تطبق فوراً على جميع التقارير الجديدة</li>
              <li>احتفظ بنسخة احتياطية من الشعار الأصلي</li>
            </ul>
          </small>
        </div>
      </div>
    </div>
  </div>
</form>

<script>
  // حفظ إعدادات الشركة
  document
    .getElementById("companyForm")
    .addEventListener("submit", function (e) {
      e.preventDefault();

      const formData = new FormData(this);
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;

      // تعطيل الزر وإظهار مؤشر التحميل
      submitBtn.disabled = true;
      submitBtn.innerHTML =
        '<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...';

      fetch("/settings/company/save", {
        method: "POST",
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            showAlert("success", data.message);
            updatePreview();
          } else {
            showAlert("error", data.message);
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          showAlert("error", "حدث خطأ في الاتصال بالخادم");
        })
        .finally(() => {
          // إعادة تفعيل الزر
          submitBtn.disabled = false;
          submitBtn.innerHTML = originalText;
        });
    });

  // تحديث المعاينة عند تغيير النصوص
  document
    .getElementById("company_name")
    .addEventListener("input", updatePreview);
  document
    .getElementById("company_subtitle")
    .addEventListener("input", updatePreview);

  // تحديث المعاينة عند تغيير الشعار
  document.getElementById("logo_file").addEventListener("change", function (e) {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function (e) {
        const previewImg = document.querySelector("#reportPreview img");
        previewImg.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  });

  function updatePreview() {
    const companyName =
      document.getElementById("company_name").value || "اسم الشركة";
    const companySubtitle =
      document.getElementById("company_subtitle").value || "وصف الشركة";

    document.getElementById("previewCompanyName").textContent = companyName;
    document.getElementById("previewCompanySubtitle").textContent =
      companySubtitle;
  }

  function previewReport() {
    // فتح نافذة جديدة لمعاينة التقرير
    window.open("/employees/", "_blank");
  }

  function resetSettings() {
    if (
      confirm(
        "هل أنت متأكد من إعادة تعيين جميع إعدادات الشركة للقيم الافتراضية؟"
      )
    ) {
      fetch("/settings/reset", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: "category=company",
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            showAlert("success", data.message);
            setTimeout(() => location.reload(), 1500);
          } else {
            showAlert("error", data.message);
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          showAlert("error", "حدث خطأ في الاتصال بالخادم");
        });
    }
  }

  function showAlert(type, message) {
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert alert-${
      type ===   "error" ? "danger" : type
    } alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container =
      document.querySelector(".container-fluid") || document.body;
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }
</script>
{% endblock %}
