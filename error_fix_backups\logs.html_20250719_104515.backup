{% extends "base.html" %} {% block title %}سجلات النظام - نظام الشؤون
القانونية{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2">
    <i class="fas fa-file-alt me-2"></i>
    سجلات النظام
  </h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
      <button
        type="button"
        class="btn btn-outline-danger"
        onclick="clearLogs()"
      >
        <i class="fas fa-trash me-1"></i>
        مسح السجلات
      </button>
      <button
        type="button"
        class="btn btn-outline-primary"
        onclick="exportLogs()"
      >
        <i class="fas fa-download me-1"></i>
        تصدير السجلات
      </button>
      <a href="{{ {% url 'admin.index' %} }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-1"></i>
        العودة للوحة التحكم
      </a>
    </div>
  </div>
</div>

<!-- إحصائيات السجلات -->
<div class="row mb-4">
  <div class="col-md-3">
    <div class="card text-white bg-info">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h4>{{ logs|length }}</h4>
            <p class="mb-0">إجمالي السجلات</p>
          </div>
          <div class="align-self-center">
            <i class="fas fa-list fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-danger">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h4>1</h4>
            <p class="mb-0">أخطاء</p>
          </div>
          <div class="align-self-center">
            <i class="fas fa-exclamation-triangle fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-warning">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h4>1</h4>
            <p class="mb-0">تحذيرات</p>
          </div>
          <div class="align-self-center">
            <i class="fas fa-exclamation-circle fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-success">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h4>1</h4>
            <p class="mb-0">معلومات</p>
          </div>
          <div class="align-self-center">
            <i class="fas fa-info-circle fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- فلترة السجلات -->
<div class="card mb-4">
  <div class="card-body">
    <form method="GET" class="row g-3">
      <div class="col-md-3">
        <label for="level" class="form-label">مستوى السجل</label>
        <select class="form-select" id="level" name="level">
          <option value="">جميع المستويات</option>
          <option value="ERROR">خطأ</option>
          <option value="WARNING">تحذير</option>
          <option value="INFO">معلومات</option>
          <option value="DEBUG">تطوير</option>
        </select>
      </div>
      <div class="col-md-3">
        <label for="user" class="form-label">المستخدم</label>
        <select class="form-select" id="user" name="user">
          <option value="">جميع المستخدمين</option>
          <option value="admin">admin</option>
          <option value="system">system</option>
          <option value="unknown">unknown</option>
        </select>
      </div>
      <div class="col-md-3">
        <label for="date_from" class="form-label">من تاريخ</label>
        <input
          type="date"
          class="form-control"
          id="date_from"
          name="date_from"
        />
      </div>
      <div class="col-md-3">
        <label for="date_to" class="form-label">إلى تاريخ</label>
        <input type="date" class="form-control" id="date_to" name="date_to" />
      </div>
      <div class="col-12">
        <div class="d-grid gap-2 d-md-flex">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-filter me-1"></i>
            تطبيق الفلتر
          </button>
          <button
            type="button"
            class="btn btn-outline-secondary"
            onclick="clearFilters()"
          >
            <i class="fas fa-times me-1"></i>
            مسح الفلتر
          </button>
          <button
            type="button"
            class="btn btn-outline-info"
            onclick="refreshLogs()"
          >
            <i class="fas fa-sync me-1"></i>
            تحديث
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- قائمة السجلات -->
<div class="card">
  <div class="card-header">
    <h5 class="mb-0">سجلات النظام</h5>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-hover table-sm">
        <thead>
          <tr>
            <th>الوقت</th>
            <th>المستوى</th>
            <th>المستخدم</th>
            <th>الرسالة</th>
            <th>الإجراءات</th>
          </tr>
        </thead>
        <tbody>
          {% for log in logs %}
          <tr>
            <td>
              <small>{{ log.timestamp|date:"%Y-%m-%d %H:%M:%S" }}</small>
            </td>
            <td>
              {% if log.level == 'ERROR' %}
              <span class="badge bg-danger">{{ log.level }}</span>
              {% elif log.level == 'WARNING' %}
              <span class="badge bg-warning">{{ log.level }}</span>
              {% elif log.level == 'INFO' %}
              <span class="badge bg-success">{{ log.level }}</span>
              {% else %}
              <span class="badge bg-secondary">{{ log.level }}</span>
              {% endif %}
            </td>
            <td>
              <small>{{ log.user }}</small>
            </td>
            <td>
              <span class="log-message" title="{{ log.message }}">
                {{ log.message|truncatechars:80 }}{% if log.message|length > 80 %}...{%
                endif %}
              </span>
            </td>
            <td>
              <button
                onclick="viewLogDetails('{{ log.id }}')"
                class="btn btn-outline-primary btn-sm"
                title="عرض التفاصيل"
              >
                <i class="fas fa-eye"></i>
              </button>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    {% if not logs %}
    <div class="text-center py-5">
      <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
      <h5 class="text-muted">لا توجد سجلات</h5>
      <p class="text-muted">لم يتم العثور على سجلات تطابق معايير البحث.</p>
    </div>
    {% endif %}

    <!-- التنقل بين الصفحات -->
    <nav aria-label="صفحات السجلات" class="mt-4">
      <ul class="pagination justify-content-center">
        <li class="page-item disabled">
          <span class="page-link">السابق</span>
        </li>
        <li class="page-item active">
          <span class="page-link">1</span>
        </li>
        <li class="page-item">
          <a
            class="page-link"
            href="#"
            onclick="alert('سيتم إضافة هذه الوظيفة قريباً')"
            >2</a
          >
        </li>
        <li class="page-item">
          <a
            class="page-link"
            href="#"
            onclick="alert('سيتم إضافة هذه الوظيفة قريباً')"
            >التالي</a
          >
        </li>
      </ul>
    </nav>
  </div>
</div>

<!-- نافذة تفاصيل السجل -->
<div class="modal fade" id="logModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تفاصيل السجل</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body" id="logModalBody">
        <!-- سيتم تحميل المحتوى هنا -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          إغلاق
        </button>
      </div>
    </div>
  </div>
</div>

<!-- نافذة تأكيد مسح السجلات -->
<div class="modal fade" id="clearLogsModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تأكيد مسح السجلات</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <p>هل أنت متأكد من رغبتك في مسح جميع السجلات؟</p>
        <div class="alert alert-danger">
          <i class="fas fa-exclamation-triangle me-2"></i>
          تحذير: لا يمكن التراجع عن هذا الإجراء.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          إلغاء
        </button>
        <button
          type="button"
          class="btn btn-danger"
          onclick="confirmClearLogs()"
        >
          مسح السجلات
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_css %}
<style>
  .log-message {
    font-family: "Courier New", monospace;
    font-size: 0.9em;
  }

  .table-sm td {
    vertical-align: middle;
  }

  .badge {
    font-size: 0.75em;
  }
</style>
{% endblock %} {% block extra_js %}
<script>
  function viewLogDetails(logId) {
    const modalBody = document.getElementById("logModalBody");
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById("logModal"));
    modal.show();

    // محاكاة تحميل البيانات
    setTimeout(() => {
      modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-3"><strong>المعرف:</strong></div>
                <div class="col-md-9">${logId}</div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-3"><strong>الوقت:</strong></div>
                <div class="col-md-9">2025-01-18 14:30:25</div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-3"><strong>المستوى:</strong></div>
                <div class="col-md-9"><span class="badge bg-info">INFO</span></div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-3"><strong>المستخدم:</strong></div>
                <div class="col-md-9">admin</div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-3"><strong>الرسالة:</strong></div>
                <div class="col-md-9">
                    <pre class="bg-light p-2 rounded">تم تسجيل دخول المستخدم admin بنجاح من العنوان *************</pre>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-3"><strong>معلومات إضافية:</strong></div>
                <div class="col-md-9">
                    <pre class="bg-light p-2 rounded">{
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "session_id": "abc123def456"
}</pre>
                </div>
            </div>
        `;
    }, 1000);
  }

  function clearLogs() {
    const modal = new bootstrap.Modal(
      document.getElementById("clearLogsModal")
    );
    modal.show();
  }

  function confirmClearLogs() {
    alert("سيتم مسح جميع السجلات (وظيفة تجريبية)");

    const modal = bootstrap.Modal.getInstance(
      document.getElementById("clearLogsModal")
    );
    modal.hide();

    location.reload();
  }

  function exportLogs() {
    alert("سيتم تصدير السجلات (وظيفة تجريبية)");
  }

  function clearFilters() {
    document.getElementById("level").value = "";
    document.getElementById("user").value = "";
    document.getElementById("date_from").value = "";
    document.getElementById("date_to").value = "";
  }

  function refreshLogs() {
    location.reload();
  }

  // تحديث السجلات كل 30 ثانية
  setInterval(function () {
    // يمكن إضافة تحديث تلقائي للسجلات هنا
    console.log("تحديث السجلات...");
  }, 30000);
</script>
{% endblock %}
