from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, date, time, timedelta
from decimal import Decimal
import uuid
import os
from permissions_manager import permission_required

bp = Blueprint('attendance', __name__, url_prefix='/attendance')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from flask import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        return {}

def get_db():
    """الحصول على كائن قاعدة البيانات"""
    try:
        from flask import current_app
        return current_app.extensions['sqlalchemy'].db
    except (RuntimeError, KeyError):
        return None

def calculate_hours_worked(check_in, check_out, break_start=None, break_end=None):
    """حساب ساعات العمل"""
    if not check_in or not check_out:
        return 0.0
    
    # تحويل إلى datetime للحساب
    today = date.today()
    check_in_dt = datetime.combine(today, check_in)
    check_out_dt = datetime.combine(today, check_out)
    
    # إذا كان وقت الانصراف في اليوم التالي
    if check_out < check_in:
        check_out_dt += timedelta(days=1)
    
    total_time = check_out_dt - check_in_dt
    
    # خصم وقت الاستراحة
    if break_start and break_end:
        break_start_dt = datetime.combine(today, break_start)
        break_end_dt = datetime.combine(today, break_end)
        if break_end < break_start:
            break_end_dt += timedelta(days=1)
        break_time = break_end_dt - break_start_dt
        total_time -= break_time
    
    return round(total_time.total_seconds() / 3600, 2)

@bp.route('/')
@login_required
@permission_required('view_attendance')
def index():
    """عرض سجلات الحضور"""
    try:
        models = get_models()
        Attendance = models.get('Attendance')
        Employee = models.get('Employee')
        Department = models.get('Department')
        
        if not all([Attendance, Employee, Department]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # فلترة البيانات
        search = request.args.get('search', '').strip()
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        status = request.args.get('status', '').strip()
        
        # بناء الاستعلام
        query = Attendance.query.join(Employee)
        
        if search:
            query = query.filter(Employee.full_name.contains(search))
        
        if employee_id:
            query = query.filter(Attendance.employee_id == employee_id)
        
        if department_id:
            query = query.filter(Employee.department_id == department_id)
        
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(Attendance.date >= date_from_obj)
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                query = query.filter(Attendance.date <= date_to_obj)
            except ValueError:
                pass
        
        if status:
            query = query.filter(Attendance.status == status)
        
        # ترتيب النتائج
        attendance_records = query.order_by(Attendance.date.desc(), Attendance.check_in.desc()).all()
        
        # جلب قوائم الفلترة
        employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
        departments = Department.query.order_by(Department.name).all()
        
        # إحصائيات سريعة
        today = date.today()
        stats = {
            'today_present': Attendance.query.filter_by(date=today, status='present').count(),
            'today_absent': Attendance.query.filter_by(date=today, status='absent').count(),
            'today_late': Attendance.query.filter_by(date=today, status='late').count(),
            'total_records': len(attendance_records)
        }
        
        return render_template('attendance/index.html',
                             attendance_records=attendance_records,
                             employees=employees,
                             departments=departments,
                             search=search,
                             employee_id=employee_id,
                             department_id=department_id,
                             date_from=date_from,
                             date_to=date_to,
                             status=status,
                             stats=stats,
                             today=today)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('view_attendance')
def add():
    """إضافة سجل حضور جديد"""
    if request.method == 'GET':
        try:
            models = get_models()
            Employee = models.get('Employee')
            
            if not Employee:
                flash('خطأ في تحميل النماذج', 'error')
                return redirect(url_for('attendance.index'))

            employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
            
            return render_template('attendance/add.html',
                                 employees=employees,
                                 today=date.today())
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
            return redirect(url_for('attendance.index'))
    
    # POST request
    try:
        models = get_models()
        Attendance = models.get('Attendance')
        Employee = models.get('Employee')
        db = get_db()
        
        if not all([Attendance, Employee, db]):
            return jsonify({'success': False, 'message': 'خطأ في تحميل النماذج'})

        # جلب البيانات من النموذج
        employee_id = request.form.get('employee_id', type=int)
        attendance_date = request.form.get('date')
        check_in_str = request.form.get('check_in', '').strip()
        check_out_str = request.form.get('check_out', '').strip()
        break_start_str = request.form.get('break_start', '').strip()
        break_end_str = request.form.get('break_end', '').strip()
        status = request.form.get('status', 'present').strip()
        notes = request.form.get('notes', '').strip()
        location = request.form.get('location', '').strip()

        # التحقق من صحة البيانات
        if not employee_id or not attendance_date:
            return jsonify({'success': False, 'message': 'الموظف والتاريخ مطلوبان'})

        # التحقق من وجود الموظف
        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'success': False, 'message': 'الموظف غير موجود'})

        # تحويل التاريخ
        try:
            attendance_date_obj = datetime.strptime(attendance_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'message': 'تنسيق التاريخ غير صحيح'})

        # التحقق من عدم وجود سجل لنفس الموظف في نفس التاريخ
        existing = Attendance.query.filter_by(employee_id=employee_id, date=attendance_date_obj).first()
        if existing:
            return jsonify({'success': False, 'message': 'يوجد سجل حضور لهذا الموظف في نفس التاريخ'})

        # تحويل الأوقات
        check_in_time = None
        check_out_time = None
        break_start_time = None
        break_end_time = None

        if check_in_str:
            try:
                check_in_time = datetime.strptime(check_in_str, '%H:%M').time()
            except ValueError:
                return jsonify({'success': False, 'message': 'تنسيق وقت الحضور غير صحيح'})

        if check_out_str:
            try:
                check_out_time = datetime.strptime(check_out_str, '%H:%M').time()
            except ValueError:
                return jsonify({'success': False, 'message': 'تنسيق وقت الانصراف غير صحيح'})

        if break_start_str:
            try:
                break_start_time = datetime.strptime(break_start_str, '%H:%M').time()
            except ValueError:
                return jsonify({'success': False, 'message': 'تنسيق وقت بداية الاستراحة غير صحيح'})

        if break_end_str:
            try:
                break_end_time = datetime.strptime(break_end_str, '%H:%M').time()
            except ValueError:
                return jsonify({'success': False, 'message': 'تنسيق وقت نهاية الاستراحة غير صحيح'})

        # حساب ساعات العمل
        hours_worked = calculate_hours_worked(check_in_time, check_out_time, break_start_time, break_end_time)

        # إنشاء سجل الحضور
        attendance = Attendance(
            employee_id=employee_id,
            date=attendance_date_obj,
            check_in=check_in_time,
            check_out=check_out_time,
            break_start=break_start_time,
            break_end=break_end_time,
            hours_worked=hours_worked,
            status=status,
            notes=notes,
            location=location,
            ip_address=request.remote_addr,
            is_manual=True,
            created_by=current_user.id
        )

        db.session.add(attendance)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إضافة سجل الحضور بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:attendance_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('manage_attendance')
def edit(attendance_id):
    """تعديل سجل حضور"""
    try:
        models = get_models()
        Attendance = models.get('Attendance')
        Employee = models.get('Employee')
        db = get_db()
        
        if not all([Attendance, Employee, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('attendance.index'))

        attendance = Attendance.query.get_or_404(attendance_id)
        
        if request.method == 'GET':
            employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
            
            return render_template('attendance/edit.html',
                                 attendance=attendance,
                                 employees=employees)
        
        # POST request
        # جلب البيانات من النموذج
        employee_id = request.form.get('employee_id', type=int)
        attendance_date = request.form.get('date')
        check_in_str = request.form.get('check_in', '').strip()
        check_out_str = request.form.get('check_out', '').strip()
        break_start_str = request.form.get('break_start', '').strip()
        break_end_str = request.form.get('break_end', '').strip()
        status = request.form.get('status', 'present').strip()
        notes = request.form.get('notes', '').strip()
        location = request.form.get('location', '').strip()

        # التحقق من صحة البيانات
        if not employee_id or not attendance_date:
            return jsonify({'success': False, 'message': 'الموظف والتاريخ مطلوبان'})

        # التحقق من وجود الموظف
        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'success': False, 'message': 'الموظف غير موجود'})

        # تحويل التاريخ
        try:
            attendance_date_obj = datetime.strptime(attendance_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'message': 'تنسيق التاريخ غير صحيح'})

        # التحقق من عدم وجود سجل آخر لنفس الموظف في نفس التاريخ
        existing = Attendance.query.filter_by(employee_id=employee_id, date=attendance_date_obj).filter(Attendance.id != attendance_id).first()
        if existing:
            return jsonify({'success': False, 'message': 'يوجد سجل حضور آخر لهذا الموظف في نفس التاريخ'})

        # تحويل الأوقات
        check_in_time = None
        check_out_time = None
        break_start_time = None
        break_end_time = None

        if check_in_str:
            try:
                check_in_time = datetime.strptime(check_in_str, '%H:%M').time()
            except ValueError:
                return jsonify({'success': False, 'message': 'تنسيق وقت الحضور غير صحيح'})

        if check_out_str:
            try:
                check_out_time = datetime.strptime(check_out_str, '%H:%M').time()
            except ValueError:
                return jsonify({'success': False, 'message': 'تنسيق وقت الانصراف غير صحيح'})

        if break_start_str:
            try:
                break_start_time = datetime.strptime(break_start_str, '%H:%M').time()
            except ValueError:
                return jsonify({'success': False, 'message': 'تنسيق وقت بداية الاستراحة غير صحيح'})

        if break_end_str:
            try:
                break_end_time = datetime.strptime(break_end_str, '%H:%M').time()
            except ValueError:
                return jsonify({'success': False, 'message': 'تنسيق وقت نهاية الاستراحة غير صحيح'})

        # حساب ساعات العمل
        hours_worked = calculate_hours_worked(check_in_time, check_out_time, break_start_time, break_end_time)

        # تحديث البيانات
        attendance.employee_id = employee_id
        attendance.date = attendance_date_obj
        attendance.check_in = check_in_time
        attendance.check_out = check_out_time
        attendance.break_start = break_start_time
        attendance.break_end = break_end_time
        attendance.hours_worked = hours_worked
        attendance.status = status
        attendance.notes = notes
        attendance.location = location

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تحديث سجل الحضور بنجاح'})

    except Exception as e:
        if request.method == 'POST':
            return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})
        else:
            flash(f'حدث خطأ: {str(e)}', 'error')
            return redirect(url_for('attendance.index'))

@bp.route('/<int:attendance_id>/delete', methods=['POST'])
@login_required
@permission_required('manage_attendance')
def delete(attendance_id):
    """حذف سجل حضور"""
    try:
        models = get_models()
        Attendance = models.get('Attendance')
        db = get_db()

        if not all([Attendance, db]):
            return jsonify({'success': False, 'message': 'خطأ في تحميل النماذج'})

        attendance = Attendance.query.get_or_404(attendance_id)

        db.session.delete(attendance)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حذف سجل الحضور بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/quick-checkin', methods=['POST'])
@login_required
@permission_required('manage_attendance')
def quick_checkin():
    """تسجيل حضور سريع"""
    try:
        models = get_models()
        Attendance = models.get('Attendance')
        Employee = models.get('Employee')
        db = get_db()

        if not all([Attendance, Employee, db]):
            return jsonify({'success': False, 'message': 'خطأ في تحميل النماذج'})

        employee_id = request.form.get('employee_id', type=int)

        if not employee_id:
            return jsonify({'success': False, 'message': 'معرف الموظف مطلوب'})

        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'success': False, 'message': 'الموظف غير موجود'})

        today = date.today()
        current_time = datetime.now().time()

        # التحقق من وجود سجل لليوم
        existing = Attendance.query.filter_by(employee_id=employee_id, date=today).first()

        if existing:
            if existing.check_out:
                return jsonify({'success': False, 'message': 'تم تسجيل الحضور والانصراف لهذا اليوم'})
            else:
                # تسجيل انصراف
                existing.check_out = current_time
                existing.hours_worked = calculate_hours_worked(existing.check_in, current_time)
                db.session.commit()
                return jsonify({'success': True, 'message': 'تم تسجيل الانصراف بنجاح'})
        else:
            # تسجيل حضور جديد
            attendance = Attendance(
                employee_id=employee_id,
                date=today,
                check_in=current_time,
                status='present',
                location='مكتب',
                ip_address=request.remote_addr,
                is_manual=False,
                created_by=current_user.id
            )
            db.session.add(attendance)
            db.session.commit()
            return jsonify({'success': True, 'message': 'تم تسجيل الحضور بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/report')
@login_required
@permission_required('view_attendance')
def report():
    """تقرير الحضور والغياب"""
    try:
        models = get_models()
        Attendance = models.get('Attendance')
        Employee = models.get('Employee')
        Department = models.get('Department')

        if not all([Attendance, Employee, Department]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # فلترة البيانات
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # تواريخ افتراضية (الشهر الحالي)
        if not date_from:
            date_from = date.today().replace(day=1).strftime('%Y-%m-%d')
        if not date_to:
            date_to = date.today().strftime('%Y-%m-%d')

        # بناء الاستعلام
        query = Attendance.query.join(Employee)

        if employee_id:
            query = query.filter(Attendance.employee_id == employee_id)

        if department_id:
            query = query.filter(Employee.department_id == department_id)

        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(Attendance.date >= date_from_obj, Attendance.date <= date_to_obj)
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return redirect(url_for('attendance.index'))

        attendance_records = query.order_by(Attendance.date.desc()).all()

        # جلب قوائم الفلترة
        employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
        departments = Department.query.order_by(Department.name).all()

        # حساب الإحصائيات
        total_records = len(attendance_records)
        present_count = len([r for r in attendance_records if r.status == 'present'])
        absent_count = len([r for r in attendance_records if r.status == 'absent'])
        late_count = len([r for r in attendance_records if r.status == 'late'])
        total_hours = sum([r.hours_worked or 0 for r in attendance_records])

        stats = {
            'total_records': total_records,
            'present_count': present_count,
            'absent_count': absent_count,
            'late_count': late_count,
            'total_hours': total_hours,
            'average_hours': total_hours / total_records if total_records > 0 else 0,
            'attendance_rate': (present_count / total_records * 100) if total_records > 0 else 0
        }

        return render_template('attendance/report.html',
                             attendance_records=attendance_records,
                             employees=employees,
                             departments=departments,
                             employee_id=employee_id,
                             department_id=department_id,
                             date_from=date_from,
                             date_to=date_to,
                             stats=stats,
                             report_title="تقرير الحضور والغياب",
                             report_period=f"من {date_from} إلى {date_to}",
                             report_date=date.today().strftime('%Y-%m-%d'))
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('attendance.index'))
