{% extends "base.html" %} {% block title %}إضافة مستخدم جديد - نظام الشؤون
القانونية{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2">
    <i class="fas fa-user-plus me-2"></i>
    إضافة مستخدم جديد
  </h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
      <a href="{{ url_for('admin_users') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-1"></i>
        العودة إلى قائمة المستخدمين
      </a>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-8">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-user me-2"></i>
          بيانات المستخدم الأساسية
        </h5>
      </div>
      <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="full_name" class="form-label"
                >الاسم الكامل <span class="text-danger">*</span></label
              >
              <input
                type="text"
                class="form-control"
                id="full_name"
                name="full_name"
                required
              />
            </div>
            <div class="col-md-6 mb-3">
              <label for="username" class="form-label"
                >اسم المستخدم <span class="text-danger">*</span></label
              >
              <input
                type="text"
                class="form-control"
                id="username"
                name="username"
                required
              />
              <div class="form-text">
                يجب أن يكون فريداً ولا يحتوي على مسافات
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="email" class="form-label"
                >البريد الإلكتروني <span class="text-danger">*</span></label
              >
              <input
                type="email"
                class="form-control"
                id="email"
                name="email"
                required
              />
            </div>
            <div class="col-md-6 mb-3">
              <label for="phone" class="form-label">رقم الهاتف</label>
              <input type="tel" class="form-control" id="phone" name="phone" />
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="password" class="form-label"
                >كلمة المرور <span class="text-danger">*</span></label
              >
              <div class="input-group">
                <input
                  type="password"
                  class="form-control"
                  id="password"
                  name="password"
                  required
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  onclick="togglePassword('password')"
                >
                  <i class="fas fa-eye" id="password-icon"></i>
                </button>
              </div>
              <div class="form-text">يجب أن تكون 8 أحرف على الأقل</div>
            </div>
            <div class="col-md-6 mb-3">
              <label for="confirm_password" class="form-label"
                >تأكيد كلمة المرور <span class="text-danger">*</span></label
              >
              <div class="input-group">
                <input
                  type="password"
                  class="form-control"
                  id="confirm_password"
                  name="confirm_password"
                  required
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  onclick="togglePassword('confirm_password')"
                >
                  <i class="fas fa-eye" id="confirm_password-icon"></i>
                </button>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="role" class="form-label"
                >الدور <span class="text-danger">*</span></label
              >
              <select class="form-select" id="role" name="role" required>
                <option value="">اختر الدور</option>
                <option value="admin">مدير النظام</option>
                <option value="lawyer">محامي</option>
                <option value="secretary">سكرتير</option>
                <option value="client">عميل</option>
              </select>
            </div>
            <div class="col-md-6 mb-3">
              <label for="department" class="form-label">القسم</label>
              <select class="form-select" id="department" name="department">
                <option value="">اختر القسم</option>
                <option value="legal">الشؤون القانونية</option>
                <option value="admin">الإدارة</option>
                <option value="finance">المالية</option>
                <option value="hr">الموارد البشرية</option>
              </select>
            </div>
          </div>

          <div class="mb-3">
            <label for="profile_image" class="form-label"
              >صورة الملف الشخصي</label
            >
            <input
              type="file"
              class="form-control"
              id="profile_image"
              name="profile_image"
              accept="image/*"
            />
            <div class="form-text">
              اختياري - يفضل صورة مربعة بحجم 200x200 بكسل
            </div>
          </div>

          <div class="mb-3">
            <label for="address" class="form-label">العنوان</label>
            <textarea
              class="form-control"
              id="address"
              name="address"
              rows="2"
            ></textarea>
          </div>

          <div class="mb-3">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea
              class="form-control"
              id="notes"
              name="notes"
              rows="3"
              placeholder="أي ملاحظات إضافية عن المستخدم..."
            ></textarea>
          </div>

          <div class="mb-3">
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                id="is_active"
                name="is_active"
                checked
              />
              <label class="form-check-label" for="is_active">
                تفعيل المستخدم فوراً
              </label>
            </div>
          </div>

          <div class="mb-3">
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                id="send_welcome_email"
                name="send_welcome_email"
                checked
              />
              <label class="form-check-label" for="send_welcome_email">
                إرسال بريد ترحيبي للمستخدم
              </label>
            </div>
          </div>

          <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <button
              type="button"
              class="btn btn-secondary me-md-2"
              onclick="history.back()"
            >
              <i class="fas fa-times me-1"></i>
              إلغاء
            </button>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save me-1"></i>
              إضافة المستخدم
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div class="col-lg-4">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fas fa-info-circle me-2"></i>
          إرشادات
        </h6>
      </div>
      <div class="card-body">
        <div class="alert alert-info">
          <h6><i class="fas fa-lightbulb me-2"></i>نصائح مهمة:</h6>
          <ul class="mb-0">
            <li>تأكد من صحة البيانات المدخلة</li>
            <li>اختر دور المستخدم بعناية</li>
            <li>استخدم كلمة مرور قوية</li>
            <li>تأكد من صحة البريد الإلكتروني</li>
          </ul>
        </div>

        <div class="alert alert-warning">
          <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه:</h6>
          <p class="mb-0">
            الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة ويجب
            ملؤها.
          </p>
        </div>
      </div>
    </div>

    <div class="card mt-3">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fas fa-users-cog me-2"></i>
          أدوار المستخدمين
        </h6>
      </div>
      <div class="card-body">
        <div class="mb-2">
          <strong>مدير النظام:</strong>
          <small class="text-muted d-block">صلاحيات كاملة على النظام</small>
        </div>
        <div class="mb-2">
          <strong>محامي:</strong>
          <small class="text-muted d-block">إدارة القضايا والعملاء</small>
        </div>
        <div class="mb-2">
          <strong>سكرتير:</strong>
          <small class="text-muted d-block">إدارة المواعيد والمستندات</small>
        </div>
        <div class="mb-2">
          <strong>عميل:</strong>
          <small class="text-muted d-block">عرض قضاياه فقط</small>
        </div>
      </div>
    </div>

    <div class="card mt-3">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fas fa-shield-alt me-2"></i>
          الأمان
        </h6>
      </div>
      <div class="card-body">
        <div class="alert alert-success">
          <small>
            <i class="fas fa-lock me-1"></i>
            جميع كلمات المرور مشفرة ومحمية.
          </small>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + "-icon");

    if (field.type === "password") {
      field.type = "text";
      icon.classList.remove("fa-eye");
      icon.classList.add("fa-eye-slash");
    } else {
      field.type = "password";
      icon.classList.remove("fa-eye-slash");
      icon.classList.add("fa-eye");
    }
  }

  // التحقق من تطابق كلمات المرور
  document
    .getElementById("confirm_password")
    .addEventListener("input", function () {
      const password = document.getElementById("password").value;
      const confirmPassword = this.value;

      if (password !== confirmPassword) {
        this.setCustomValidity("كلمات المرور غير متطابقة");
        this.classList.add("is-invalid");
      } else {
        this.setCustomValidity("");
        this.classList.remove("is-invalid");
        this.classList.add("is-valid");
      }
    });

  // التحقق من قوة كلمة المرور
  document.getElementById("password").addEventListener("input", function () {
    const password = this.value;
    const minLength = 8;

    if (password.length < minLength) {
      this.setCustomValidity(
        `كلمة المرور يجب أن تكون ${minLength} أحرف على الأقل`
      );
      this.classList.add("is-invalid");
    } else {
      this.setCustomValidity("");
      this.classList.remove("is-invalid");
      this.classList.add("is-valid");
    }
  });

  // التحقق من صحة اسم المستخدم
  document.getElementById("username").addEventListener("input", function () {
    const username = this.value;
    const regex = /^[a-zA-Z0-9_]+$/;

    if (!regex.test(username)) {
      this.setCustomValidity("اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط");
      this.classList.add("is-invalid");
    } else {
      this.setCustomValidity("");
      this.classList.remove("is-invalid");
      this.classList.add("is-valid");
    }
  });

  // معاينة الصورة
  document
    .getElementById("profile_image")
    .addEventListener("change", function (e) {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
          // يمكن إضافة معاينة للصورة هنا
        };
        reader.readAsDataURL(file);
      }
    });

  // التركيز على أول حقل
  document.addEventListener("DOMContentLoaded", function () {
    document.getElementById("full_name").focus();
  });
</script>
{% endblock %}
