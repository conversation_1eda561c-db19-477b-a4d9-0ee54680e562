#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لقواعد البيانات
Comprehensive Database Fix
"""

import sqlite3
import os
import sys
from datetime import datetime

def check_and_fix_database():
    """فحص وإصلاح شامل لقاعدة البيانات"""
    
    # قواعد البيانات المحتملة
    databases = [
        'legal_system.db',
        'instance/legal_system.db'
    ]
    
    results = {}
    
    for db_path in databases:
        if os.path.exists(db_path):
            print(f"\n🔍 فحص قاعدة البيانات: {db_path}")
            results[db_path] = fix_single_database(db_path)
        else:
            print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
    
    return results

def fix_single_database(db_path):
    """إصلاح قاعدة بيانات واحدة"""
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 الجداول الموجودة: {', '.join(existing_tables)}")
        
        # الجداول المطلوبة مع مخططاتها
        required_tables = {
            'users': """
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(80) UNIQUE NOT NULL,
                    email VARCHAR(120) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(200) NOT NULL,
                    phone VARCHAR(20),
                    role VARCHAR(50) DEFAULT 'user',
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """,
            'employees': """
                CREATE TABLE IF NOT EXISTS employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_number VARCHAR(50) UNIQUE NOT NULL,
                    full_name VARCHAR(200) NOT NULL,
                    email VARCHAR(120),
                    phone VARCHAR(20),
                    national_id VARCHAR(20) UNIQUE,
                    department_id INTEGER,
                    position VARCHAR(100),
                    hire_date DATE,
                    basic_salary DECIMAL(10,2) DEFAULT 0.0,
                    allowances DECIMAL(10,2) DEFAULT 0.0,
                    deductions DECIMAL(10,2) DEFAULT 0.0,
                    total_salary DECIMAL(10,2) DEFAULT 0.0,
                    status VARCHAR(20) DEFAULT 'active',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (department_id) REFERENCES departments (id)
                )
            """,
            'departments': """
                CREATE TABLE IF NOT EXISTS departments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    manager_id INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (manager_id) REFERENCES employees (id)
                )
            """,
            'attendance': """
                CREATE TABLE IF NOT EXISTS attendance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    date DATE NOT NULL,
                    check_in TIME,
                    check_out TIME,
                    break_start TIME,
                    break_end TIME,
                    hours_worked DECIMAL(4,2),
                    overtime_hours DECIMAL(4,2) DEFAULT 0.0,
                    status VARCHAR(20) DEFAULT 'present',
                    notes TEXT,
                    location VARCHAR(100),
                    ip_address VARCHAR(45),
                    is_manual BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    FOREIGN KEY (employee_id) REFERENCES employees (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            """,
            'leave_requests': """
                CREATE TABLE IF NOT EXISTS leave_requests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    leave_type VARCHAR(50) NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    days_count INTEGER NOT NULL,
                    reason TEXT NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending',
                    approved_by INTEGER,
                    approved_at DATETIME,
                    rejection_reason TEXT,
                    attachment_path VARCHAR(255),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES employees (id),
                    FOREIGN KEY (approved_by) REFERENCES users (id)
                )
            """,
            'warnings': """
                CREATE TABLE IF NOT EXISTS warnings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    warning_type VARCHAR(50) NOT NULL,
                    category VARCHAR(50) NOT NULL,
                    title VARCHAR(200) NOT NULL,
                    description TEXT NOT NULL,
                    incident_date DATE NOT NULL,
                    severity VARCHAR(20) DEFAULT 'medium',
                    action_required TEXT,
                    deadline DATE,
                    status VARCHAR(20) DEFAULT 'active',
                    follow_up_date DATE,
                    resolution_notes TEXT,
                    attachment_path VARCHAR(255),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES employees (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            """,
            'penalties': """
                CREATE TABLE IF NOT EXISTS penalties (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    penalty_type VARCHAR(50) NOT NULL,
                    category VARCHAR(50) NOT NULL,
                    title VARCHAR(200) NOT NULL,
                    description TEXT NOT NULL,
                    incident_date DATE NOT NULL,
                    penalty_date DATE NOT NULL,
                    amount DECIMAL(10,2),
                    days_count INTEGER,
                    severity VARCHAR(20) DEFAULT 'medium',
                    status VARCHAR(20) DEFAULT 'active',
                    payment_status VARCHAR(20) DEFAULT 'pending',
                    notes TEXT,
                    attachment_path VARCHAR(255),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES employees (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            """
        }
        
        # إنشاء أو تحديث الجداول
        for table_name, create_sql in required_tables.items():
            try:
                if table_name not in existing_tables:
                    print(f"🔄 إنشاء جدول {table_name}...")
                    cursor.execute(create_sql)
                else:
                    print(f"✅ جدول {table_name} موجود")
                    # فحص وإصلاح الأعمدة المفقودة
                    fix_table_columns(cursor, table_name)
            except Exception as e:
                print(f"❌ خطأ في جدول {table_name}: {e}")
        
        # حفظ التغييرات
        conn.save()
        
        # فحص النتائج
        print("\n📊 إحصائيات قاعدة البيانات:")
        for table_name in required_tables.keys():
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  - {table_name}: {count} سجل")
            except:
                print(f"  - {table_name}: خطأ في القراءة")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات {db_path}: {e}")
        return False

def fix_table_columns(cursor, table_name):
    """إصلاح الأعمدة المفقودة في الجداول"""
    
    # الأعمدة المطلوبة لكل جدول
    required_columns = {
        'employees': [
            ('deductions', 'DECIMAL(10,2) DEFAULT 0.0'),
            ('total_salary', 'DECIMAL(10,2) DEFAULT 0.0'),
            ('status', 'VARCHAR(20) DEFAULT "active"')
        ],
        'penalties': [
            ('category', 'VARCHAR(50) NOT NULL DEFAULT "general"'),
            ('title', 'VARCHAR(200) NOT NULL DEFAULT ""'),
            ('description', 'TEXT NOT NULL DEFAULT ""'),
            ('incident_date', 'DATE'),
            ('amount', 'DECIMAL(10,2)'),
            ('days_count', 'INTEGER'),
            ('payment_status', 'VARCHAR(20) DEFAULT "pending"'),
            ('notes', 'TEXT'),
            ('attachment_path', 'VARCHAR(255)')
        ],
        'warnings': [
            ('category', 'VARCHAR(50) NOT NULL DEFAULT "general"'),
            ('title', 'VARCHAR(200) NOT NULL DEFAULT ""'),
            ('description', 'TEXT NOT NULL DEFAULT ""')
        ]
    }
    
    if table_name not in required_columns:
        return
    
    # فحص الأعمدة الموجودة
    cursor.execute(f"PRAGMA table_info({table_name})")
    existing_columns = [row[1] for row in cursor.fetchall()]
    
    # إضافة الأعمدة المفقودة
    for column_name, column_def in required_columns[table_name]:
        if column_name not in existing_columns:
            try:
                cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_def}")
                print(f"  ✅ تم إضافة عمود {column_name} إلى {table_name}")
            except Exception as e:
                print(f"  ❌ خطأ في إضافة عمود {column_name}: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 بدء الإصلاح الشامل لقواعد البيانات...")
    print("=" * 60)
    
    results = check_and_fix_database()
    
    print("\n" + "=" * 60)
    print("📋 ملخص النتائج:")
    
    success_count = 0
    for db_path, success in results.items():
        if success:
            print(f"✅ {db_path}: تم الإصلاح بنجاح")
            success_count += 1
        else:
            print(f"❌ {db_path}: فشل في الإصلاح")
    
    if success_count > 0:
        print(f"\n🎉 تم إصلاح {success_count} قاعدة بيانات بنجاح!")
        return True
    else:
        print("\n❌ فشل في إصلاح قواعد البيانات")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
