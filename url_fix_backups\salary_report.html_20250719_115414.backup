{% extends "base.html" %}

{% block title %}تقرير الرواتب - {{ company_name or "نظام الشؤون القانونية" }}{% endblock %}

{% block content %}
<!-- رأس التقرير للطباعة -->
{% include 'components/print_header.html' with context %}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom no-print">
    <h1 class="h2">
        <i class="fas fa-money-bill-wave me-2"></i>
        تقرير الرواتب
        {% if employee %}
        - {{ employee.full_name }}
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0 no-print">
        <div class="btn-group me-2">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print me-1"></i>
                طباعة التقرير
            </button>
            <button onclick="exportToPDF()" class="btn btn-success">
                <i class="fas fa-file-pdf me-1"></i>
                تصدير PDF
            </button>
            <a href="{{ {% url 'employees.index' %} }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للموظفين
            </a>
        </div>
    </div>
</div>

<!-- معلومات التقرير -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات التقرير
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>نوع التقرير:</strong><br>
                        <span class="badge bg-info">تقرير الرواتب</span>
                    </div>
                    <div class="col-md-3">
                        <strong>فترة التقرير:</strong><br>
                        {{ report_period or "الشهر الحالي" }}
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ الإنشاء:</strong><br>
                        {{ report_date or datetime.now()|strftime("%Y-%m-%d") }}
                    </div>
                    <div class="col-md-3">
                        <strong>عدد الموظفين:</strong><br>
                        <span class="badge bg-success">{{ employees|length if employees else (1 if employee else 0) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if employee %}
<!-- تقرير راتب موظف واحد -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    تفاصيل راتب الموظف: {{ employee.full_name }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- معلومات الموظف -->
                    <div class="col-md-6">
                        <h6 class="text-primary">المعلومات الأساسية</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>رقم الموظف:</strong></td>
                                <td>{{ employee.employee_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>الاسم الكامل:</strong></td>
                                <td>{{ employee.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>القسم:</strong></td>
                                <td>{{ employee.department.name if employee.department else 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>المنصب:</strong></td>
                                <td>{{ employee.position or 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التوظيف:</strong></td>
                                <td>{{ employee.hire_date|strftime("%Y-%m-%d") if employee.hire_date else 'غير محدد' }}</td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- تفاصيل الراتب -->
                    <div class="col-md-6">
                        <h6 class="text-success">تفاصيل الراتب</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الراتب الأساسي:</strong></td>
                                <td class="text-end">{{ "{:,.2f}"|employee.basic_salary if employee.basic_salary else '0.00' }} ريال</td>
                            </tr>
                            <tr>
                                <td><strong>البدلات:</strong></td>
                                <td class="text-end">{{ "{:,.2f}"|employee.allowances if employee.allowances else '0.00' }} ريال</td>
                            </tr>
                            <tr>
                                <td><strong>الخصومات:</strong></td>
                                <td class="text-end text-danger">{{ "{:,.2f}"|employee.deductions if employee.deductions else '0.00' }} ريال</td>
                            </tr>
                            <tr class="table-success">
                                <td><strong>صافي الراتب:</strong></td>
                                <td class="text-end"><strong>{{ "{:,.2f}"|employee.total_salary if employee.total_salary else '0.00' }} ريال</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- تقرير رواتب جميع الموظفين -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    تقرير رواتب جميع الموظفين
                </h5>
            </div>
            <div class="card-body">
                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم الموظف</th>
                                <th>الاسم</th>
                                <th>القسم</th>
                                <th>المنصب</th>
                                <th class="text-end">الراتب الأساسي</th>
                                <th class="text-end">البدلات</th>
                                <th class="text-end">الخصومات</th>
                                <th class="text-end">صافي الراتب</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set total_basic = 0 %}
                            {% set total_allowances = 0 %}
                            {% set total_deductions = 0 %}
                            {% set total_net = 0 %}
                            
                            {% for emp in employees %}
                            {% set basic = emp.basic_salary or 0 %}
                            {% set allowances = emp.allowances or 0 %}
                            {% set deductions = emp.deductions or 0 %}
                            {% set net = emp.total_salary or 0 %}
                            
                            {% set total_basic = total_basic + basic %}
                            {% set total_allowances = total_allowances + allowances %}
                            {% set total_deductions = total_deductions + deductions %}
                            {% set total_net = total_net + net %}
                            
                            <tr>
                                <td>{{ emp.employee_number }}</td>
                                <td>{{ emp.full_name }}</td>
                                <td>{{ emp.department.name if emp.department else 'غير محدد' }}</td>
                                <td>{{ emp.position or 'غير محدد' }}</td>
                                <td class="text-end">{{ "{:,.2f}"|basic }}</td>
                                <td class="text-end">{{ "{:,.2f}"|allowances }}</td>
                                <td class="text-end text-danger">{{ "{:,.2f}"|deductions }}</td>
                                <td class="text-end"><strong>{{ "{:,.2f}"|net }}</strong></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-success">
                            <tr>
                                <th colspan="4">الإجمالي</th>
                                <th class="text-end">{{ "{:,.2f}"|total_basic }} ريال</th>
                                <th class="text-end">{{ "{:,.2f}"|total_allowances }} ريال</th>
                                <th class="text-end text-danger">{{ "{:,.2f}"|total_deductions }} ريال</th>
                                <th class="text-end"><strong>{{ "{:,.2f}"|total_net }} ريال</strong></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5>لا توجد بيانات رواتب</h5>
                    <p class="text-muted">لم يتم العثور على بيانات رواتب للعرض في هذا التقرير</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- إحصائيات سريعة -->
{% if employees %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>{{ employees|length }}</h4>
                <p class="mb-0">إجمالي الموظفين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h4>{{ "{:,.0f}"|employees|sum(attribute='basic_salary') or 0 }}</h4>
                <p class="mb-0">إجمالي الرواتب الأساسية</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                <h4>{{ "{:,.0f}"|employees|sum(attribute='allowances') or 0 }}</h4>
                <p class="mb-0">إجمالي البدلات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <i class="fas fa-calculator fa-2x mb-2"></i>
                <h4>{{ "{:,.0f}"|employees|sum(attribute='total_salary') or 0 }}</h4>
                <p class="mb-0">صافي الرواتب</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- التوقيعات للطباعة -->
{% include 'components/print_signatures.html' with context %}

<script>
function exportToPDF() {
    // تحويل الصفحة إلى PDF
    window.print();
}

// تحسينات الطباعة
window.addEventListener('beforeprint', function() {
    document.title = 'تقرير الرواتب - {{ company_name or "نظام الشؤون القانونية" }}';
});

window.addEventListener('afterprint', function() {
    document.title = '{{ self.title() }}';
});
</script>

<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        border: 1px solid #333 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
        border-bottom: 1px solid #333 !important;
    }
    
    .table {
        font-size: 11pt;
    }
    
    .table th,
    .table td {
        border: 1px solid #333 !important;
        padding: 6px !important;
    }
    
    .table-dark th {
        background-color: #333 !important;
        color: white !important;
    }
    
    .table-success {
        background-color: #d4edda !important;
    }
    
    .text-danger {
        color: #dc3545 !important;
    }
    
    .text-success {
        color: #198754 !important;
    }
    
    .text-primary {
        color: #0d6efd !important;
    }
    
    .badge {
        background-color: #6c757d !important;
        color: white !important;
        padding: 2px 6px !important;
        border-radius: 3px !important;
    }
    
    .bg-success {
        background-color: #198754 !important;
        color: white !important;
    }
    
    .bg-info {
        background-color: #0dcaf0 !important;
        color: white !important;
    }
    
    .bg-primary {
        background-color: #0d6efd !important;
        color: white !important;
    }
    
    .bg-warning {
        background-color: #ffc107 !important;
        color: #000 !important;
    }
}
</style>
{% endblock %}
