@echo off
chcp 65001 >nul
title نظام الشؤون القانونية

echo.
echo ========================================
echo    نظام الشؤون القانونية
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    pause
    exit /b 1
)

REM تثبيت المتطلبات إذا لم تكن موجودة
if not exist "venv" (
    echo 📦 إنشاء البيئة الافتراضية...
    python -m venv venv
)

REM تفعيل البيئة الافتراضية
call venv\Scripts\activate.bat

REM تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt

REM تشغيل التطبيق
echo.
echo 🚀 تشغيل الخادم...
echo.
python app.py

REM إيقاف مؤقت عند الانتهاء
echo.
echo تم إيقاف الخادم
pause
