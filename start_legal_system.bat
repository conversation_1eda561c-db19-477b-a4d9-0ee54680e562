@echo off
chcp 65001 >nul
title نظام الشؤون القانونية - خادم النظام

echo.
echo ================================================================================
echo                        نظام الشؤون القانونية
echo                     Legal Affairs Management System
echo ================================================================================
echo.

echo 🔧 جاري التحقق من متطلبات النظام...

:: التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.7 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

:: التحقق من وجود ملف التطبيق
if not exist "app.py" (
    echo ❌ ملف app.py غير موجود
    echo تأكد من وجودك في مجلد النظام الصحيح
    pause
    exit /b 1
)

echo ✅ ملفات النظام موجودة

:: التحقق من المكتبات المطلوبة
echo 🔍 جاري التحقق من المكتبات المطلوبة...
python -c "import flask, flask_sqlalchemy, flask_login" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  بعض المكتبات المطلوبة غير مثبتة
    echo 📦 جاري تثبيت المكتبات...
    if exist "requirements.txt" (
        pip install -r requirements.txt
    ) else (
        pip install flask flask-sqlalchemy flask-login werkzeug
    )
)

echo ✅ جميع المكتبات متوفرة

:: قراءة إعدادات الخادم
set "server_port=5000"
if exist "server_config.json" (
    echo 📖 قراءة إعدادات الخادم...
    for /f "tokens=2 delims=:" %%a in ('findstr "port" server_config.json') do (
        set "port_line=%%a"
        goto :port_found
    )
    :port_found
    for /f "tokens=1 delims=," %%b in ("!port_line!") do (
        set "server_port=%%b"
    )
    set server_port=!server_port: =!
    set server_port=!server_port:"=!
)

:: عرض معلومات الشبكة
echo.
echo 🌐 معلومات الشبكة:
echo ================================================================================

:: الحصول على IP المحلي
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    set "ip=%%a"
    goto :found_ip
)
:found_ip
set ip=%ip: =%

echo 🖥️  عنوان IP المحلي: %ip%
echo 🔌 المنفذ المكون: %server_port%
echo.
echo 📍 روابط الوصول:
echo    المحلي:        http://localhost:%server_port%
echo    من الشبكة:     http://%ip%:%server_port%
echo.
echo 🔐 بيانات الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور:   admin123
echo.
echo 💡 نصائح مهمة:
echo    • لتغيير إعدادات الخادم: اذهب لإعدادات النظام بعد تسجيل الدخول
echo    • لفتح المنفذ في جدار الحماية: 
echo      netsh advfirewall firewall add rule name="Legal_System" dir=in action=allow protocol=TCP localport=%server_port%
echo    • تأكد من اتصال جميع الأجهزة بنفس الشبكة
echo    • يمكنك تغيير إعدادات الخادم من واجهة النظام دون إعادة تشغيل
echo.
echo ================================================================================

:: بدء تشغيل الخادم
echo 🚀 جاري تشغيل خادم النظام...
echo.
echo ⚠️  لإيقاف الخادم اضغط Ctrl+C
echo 💡 لفتح النظام في المتصفح اضغط Ctrl+Click على الرابط أعلاه
echo 🔧 لتغيير إعدادات الخادم: سجل دخول كمدير ← إعدادات النظام ← إعدادات الخادم
echo.

:: تشغيل التطبيق
python app.py

:: في حالة إغلاق التطبيق
echo.
echo 🛑 تم إيقاف الخادم
echo 📝 لمراجعة دليل الإعداد، راجع ملف README_SERVER_SETUP.md
pause
