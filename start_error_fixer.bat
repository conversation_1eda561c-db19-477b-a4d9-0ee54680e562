@echo off
chcp 65001 >nul
title نظام معالجة الأخطاء التلقائي

echo.
echo ========================================
echo      نظام معالجة الأخطاء التلقائي
echo ========================================
echo.

echo 1. تشغيل الإصلاح الشامل
echo 2. تشغيل المراقب المستمر
echo 3. إصلاح سريع للقوالب
echo 4. إصلاح سريع لملفات Python
echo 5. إصلاح سريع لملفات JavaScript
echo 6. إصلاح طوارئ
echo 7. عرض التقرير الأخير
echo 8. خروج
echo.

set /p choice="اختر (1-8): "

if "%choice%"=="1" (
    echo تشغيل الإصلاح الشامل...
    python auto_error_fixer.py
    pause
) else if "%choice%"=="2" (
    echo تشغيل المراقب المستمر...
    echo اضغط Ctrl+C للإيقاف
    python error_monitor.py
) else if "%choice%"=="3" (
    echo إصلاح سريع للقوالب...
    python fix_errors.py --templates
    pause
) else if "%choice%"=="4" (
    echo إصلاح سريع لملفات Python...
    python fix_errors.py --python
    pause
) else if "%choice%"=="5" (
    echo إصلاح سريع لملفات JavaScript...
    python fix_errors.py --javascript
    pause
) else if "%choice%"=="6" (
    echo إصلاح طوارئ...
    python fix_errors.py --emergency
    pause
) else if "%choice%"=="7" (
    echo عرض آخر تقرير...
    for /f %%i in ('dir /b /od error_fix_report_*.txt 2^>nul') do set latest=%%i
    if defined latest (
        type "%latest%"
    ) else (
        echo لا توجد تقارير متاحة
    )
    pause
) else if "%choice%"=="8" (
    exit
) else (
    echo اختيار غير صحيح
    pause
)

goto :eof
