from django.shortcuts import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime
import uuid
import os
from werkzeug.utils import secure_filename
from permissions_manager import permission_required

bp = Blueprint('settings', __name__, url_prefix='/settings')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from django.shortcuts import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        return {}

def get_db():
    """الحصول على كائن قاعدة البيانات"""
    try:
        from django.shortcuts import current_app
        return current_app.extensions['sqlalchemy'].db
    except (RuntimeError, KeyError):
        return None

def allowed_file(filename):
    """التحقق من أن نوع الملف مسموح للشعارات"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'svg', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    """التأكد من وجود مجلد رفع الشعارات"""
    upload_folder = os.path.join(current_app.root_path, 'static', 'images', 'logos')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    return upload_folder

@bp.route('/')
@login_required
@permission_required('manage_system_settings')
def index():
    """عرض جميع الإعدادات"""
    try:
        models = get_models()
        SystemSettings = models.get('SystemSettings')
        
        if not SystemSettings:
            flash('خطأ في تحميل نماذج الإعدادات', 'error')
            return redirect(url_for('dashboard'))

        # جلب الإعدادات مجمعة حسب الفئة
        settings_by_category = {}
        all_settings = SystemSettings.query.filter_by(is_active=True).order_by(SystemSettings.category, SystemSettings.display_name).all()
        
        for setting in all_settings:
            category = setting.category
            if category not in settings_by_category:
                settings_by_category[category] = []
            settings_by_category[category].append(setting)
        
        # أسماء الفئات بالعربية
        category_names = {
            'general': 'إعدادات عامة',
            'company': 'معلومات الشركة',
            'reports': 'إعدادات التقارير',
            'system': 'إعدادات النظام'
        }
        
        return render('settings/index.html',
                             settings_by_category=settings_by_category,
                             category_names=category_names)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/company')
@login_required
@permission_required('manage_system_settings')
def company():
    """إعدادات الشركة"""
    try:
        models = get_models()
        SystemSettings = models.get('SystemSettings')
        
        if not SystemSettings:
            flash('خطأ في تحميل نماذج الإعدادات', 'error')
            return redirect(url_for('dashboard'))

        # الحصول على الإعدادات الحالية
        company_name = SystemSettings.get_setting('company_name', 'مكتب الشؤون القانونية')
        company_subtitle = SystemSettings.get_setting('company_subtitle', 'نظام إدارة الموارد البشرية والشؤون القانونية')
        company_address = SystemSettings.get_setting('company_address', '')
        company_phone = SystemSettings.get_setting('company_phone', '')
        company_email = SystemSettings.get_setting('company_email', '')
        company_website = SystemSettings.get_setting('company_website', '')
        logo_path = SystemSettings.get_setting('company_logo', 'images/logo.svg')
        
        return render('settings/company.html',
                             company_name=company_name,
                             company_subtitle=company_subtitle,
                             company_address=company_address,
                             company_phone=company_phone,
                             company_email=company_email,
                             company_website=company_website,
                             logo_path=logo_path)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('settings.index'))

@bp.route('/company/save', methods=['POST'])
@login_required
@permission_required('manage_system_settings')
def save_company():
    """حفظ إعدادات الشركة"""
    try:
        models = get_models()
        SystemSettings = models.get('SystemSettings')
        db = get_db()
        
        if not all([SystemSettings, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        # جلب البيانات من النموذج
        company_name = request.POST.get('company_name', '').strip()
        company_subtitle = request.POST.get('company_subtitle', '').strip()
        company_address = request.POST.get('company_address', '').strip()
        company_phone = request.POST.get('company_phone', '').strip()
        company_email = request.POST.get('company_email', '').strip()
        company_website = request.POST.get('company_website', '').strip()
        
        # حفظ الإعدادات النصية
        settings_to_save = [
            ('company_name', company_name, 'text', 'company', 'اسم الشركة', 'اسم الشركة الذي يظهر في التقارير'),
            ('company_subtitle', company_subtitle, 'text', 'company', 'وصف الشركة', 'الوصف الذي يظهر تحت اسم الشركة'),
            ('company_address', company_address, 'text', 'company', 'عنوان الشركة', 'العنوان الكامل للشركة'),
            ('company_phone', company_phone, 'text', 'company', 'هاتف الشركة', 'رقم هاتف الشركة'),
            ('company_email', company_email, 'text', 'company', 'بريد الشركة', 'البريد الإلكتروني للشركة'),
            ('company_website', company_website, 'text', 'company', 'موقع الشركة', 'الموقع الإلكتروني للشركة')
        ]
        
        for key, value, setting_type, category, display_name, description in settings_to_save:
            SystemSettings.set_setting(key, value, setting_type, category, display_name, description, current_user.id)
        
        # معالجة رفع الشعار
        if 'logo_file' in request.files:
            file = request.files['logo_file']
            if file and file.filename and allowed_file(file.filename):
                # التحقق من حجم الملف (5 ميجابايت)
                file.seek(0, 2)
                file_size = file.tell()
                file.seek(0)
                
                if file_size > 5 * 1024 * 1024:  # 5 MB
                    return JsonResponse({'success': False, 'message': 'حجم الشعار كبير جداً (الحد الأقصى 5 ميجابايت)'})
                
                # إنشاء مجلد الشعارات
                logos_folder = ensure_upload_folder()
                
                # إنشاء اسم ملف فريد
                original_filename = secure_filename(file.filename)
                file_extension = original_filename.rsplit('.', 1)[1].lower()
                unique_filename = f"logo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
                file_path = os.path.join(logos_folder, unique_filename)
                
                # حفظ الملف
                file.save(file_path)
                
                # تحديث مسار الشعار في الإعدادات
                logo_relative_path = f"images/logos/{unique_filename}"
                SystemSettings.set_setting('company_logo', logo_relative_path, 'file', 'company', 
                                         'شعار الشركة', 'الشعار الذي يظهر في التقارير', current_user.id)
        
        db.save()
        
        return JsonResponse({'success': True, 'message': 'تم حفظ إعدادات الشركة بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/reports')
@login_required
@permission_required('manage_system_settings')
def reports():
    """إعدادات التقارير"""
    try:
        models = get_models()
        SystemSettings = models.get('SystemSettings')
        
        if not SystemSettings:
            flash('خطأ في تحميل نماذج الإعدادات', 'error')
            return redirect(url_for('dashboard'))

        # الحصول على إعدادات التقارير
        show_logo = SystemSettings.get_setting('reports_show_logo', 'true')
        show_company_info = SystemSettings.get_setting('reports_show_company_info', 'true')
        show_print_time = SystemSettings.get_setting('reports_show_print_time', 'true')
        show_signatures = SystemSettings.get_setting('reports_show_signatures', 'true')
        footer_text = SystemSettings.get_setting('reports_footer_text', 'تم إنشاء هذا التقرير تلقائياً بواسطة النظام')
        
        return render('settings/reports.html',
                             show_logo=show_logo,
                             show_company_info=show_company_info,
                             show_print_time=show_print_time,
                             show_signatures=show_signatures,
                             footer_text=footer_text)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('settings.index'))

@bp.route('/reports/save', methods=['POST'])
@login_required
@permission_required('manage_system_settings')
def save_reports():
    """حفظ إعدادات التقارير"""
    try:
        models = get_models()
        SystemSettings = models.get('SystemSettings')
        db = get_db()
        
        if not all([SystemSettings, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        # جلب البيانات من النموذج
        show_logo = 'true' if request.POST.get('show_logo') == 'on' else 'false'
        show_company_info = 'true' if request.POST.get('show_company_info') == 'on' else 'false'
        show_print_time = 'true' if request.POST.get('show_print_time') == 'on' else 'false'
        show_signatures = 'true' if request.POST.get('show_signatures') == 'on' else 'false'
        footer_text = request.POST.get('footer_text', '').strip()
        
        # حفظ الإعدادات
        settings_to_save = [
            ('reports_show_logo', show_logo, 'boolean', 'reports', 'إظهار الشعار', 'إظهار شعار الشركة في التقارير'),
            ('reports_show_company_info', show_company_info, 'boolean', 'reports', 'إظهار معلومات الشركة', 'إظهار اسم ووصف الشركة'),
            ('reports_show_print_time', show_print_time, 'boolean', 'reports', 'إظهار وقت الطباعة', 'إظهار تاريخ ووقت الطباعة'),
            ('reports_show_signatures', show_signatures, 'boolean', 'reports', 'إظهار التوقيعات', 'إظهار مربعات التوقيعات'),
            ('reports_footer_text', footer_text, 'text', 'reports', 'نص الذيل', 'النص الذي يظهر في ذيل التقرير')
        ]
        
        for key, value, setting_type, category, display_name, description in settings_to_save:
            SystemSettings.set_setting(key, value, setting_type, category, display_name, description, current_user.id)
        
        db.save()
        
        return JsonResponse({'success': True, 'message': 'تم حفظ إعدادات التقارير بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/reset', methods=['POST'])
@login_required
@permission_required('manage_system_settings')
def reset_settings():
    """إعادة تعيين الإعدادات للقيم الافتراضية"""
    try:
        models = get_models()
        SystemSettings = models.get('SystemSettings')
        db = get_db()
        
        if not all([SystemSettings, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        category = request.POST.get('category', 'all')
        
        if category == 'all':
            # حذف جميع الإعدادات
            SystemSettings.query.delete()
        else:
            # حذف إعدادات فئة معينة
            SystemSettings.query.filter_by(category=category).delete()
        
        db.save()
        
        return JsonResponse({'success': True, 'message': 'تم إعادة تعيين الإعدادات بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})
