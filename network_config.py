"""
إعدادات الشبكة لنظام الشؤون القانونية
"""

import socket
import subprocess
import platform
import os

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # إنشاء اتصال وهمي للحصول على IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        try:
            # طريقة بديلة
            hostname = socket.gethostname()
            return socket.gethostbyname(hostname)
        except Exception:
            return "127.0.0.1"

def check_port_availability(port=5000):
    """التحقق من توفر المنفذ"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        result = s.connect_ex(('localhost', port))
        s.close()
        return result != 0
    except Exception:
        return False

def get_network_interfaces():
    """الحصول على جميع واجهات الشبكة"""
    interfaces = []
    try:
        if platform.system() == "Windows":
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, shell=True)
            output = result.stdout
            # تحليل مبسط لـ ipconfig
            lines = output.split('\n')
            for line in lines:
                if 'IPv4' in line and '192.168.' in line:
                    ip = line.split(':')[-1].strip()
                    interfaces.append(ip)
        else:
            # للأنظمة الأخرى (Linux/Mac)
            result = subprocess.run(['hostname', '-I'], capture_output=True, text=True)
            if result.returncode == 0:
                ips = result.stdout.strip().split()
                interfaces.extend(ips)
    except Exception:
        pass
    
    return interfaces

def configure_firewall_windows(port=5000):
    """إعداد جدار الحماية في Windows"""
    try:
        # إضافة قاعدة جدار الحماية للمنفذ
        rule_name = f"Legal_System_Port_{port}"
        
        # حذف القاعدة إذا كانت موجودة
        subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'delete', 'rule', 
            f'name={rule_name}'
        ], capture_output=True)
        
        # إضافة قاعدة جديدة
        result = subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'add', 'rule',
            f'name={rule_name}',
            'dir=in',
            'action=allow',
            'protocol=TCP',
            f'localport={port}'
        ], capture_output=True, text=True)
        
        return result.returncode == 0
    except Exception:
        return False

def print_network_info(port=5000):
    """طباعة معلومات الشبكة"""
    local_ip = get_local_ip()
    interfaces = get_network_interfaces()
    port_available = check_port_availability(port)
    
    print("\n" + "="*80)
    print("🌐 معلومات الشبكة - نظام الشؤون القانونية")
    print("="*80)
    
    print(f"🖥️  عنوان IP المحلي الرئيسي: {local_ip}")
    
    if interfaces:
        print("📡 جميع عناوين IP المتاحة:")
        for i, ip in enumerate(interfaces, 1):
            print(f"   {i}. {ip}")
    
    print(f"🔌 المنفذ {port}: {'متاح ✅' if port_available else 'مستخدم ❌'}")
    
    print("\n📍 روابط الوصول:")
    print(f"   المحلي:        http://localhost:{port}")
    print(f"   من الشبكة:     http://{local_ip}:{port}")
    
    if interfaces:
        print("   روابط إضافية:")
        for ip in interfaces:
            if ip != local_ip:
                print(f"                  http://{ip}:{port}")
    
    print("\n🔐 بيانات الدخول الافتراضية:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور:   admin123")
    
    print("\n⚠️  ملاحظات مهمة:")
    print("   • تأكد من اتصال الأجهزة بنفس الشبكة")
    print("   • قد تحتاج لفتح المنفذ في جدار الحماية")
    print("   • استخدم HTTPS في البيئة الإنتاجية")
    
    if platform.system() == "Windows":
        print(f"\n🛡️  لفتح المنفذ في جدار الحماية Windows:")
        print(f"   netsh advfirewall firewall add rule name=\"Legal_System\" dir=in action=allow protocol=TCP localport={port}")
    
    print("="*80)

def setup_network_access(port=5000):
    """إعداد الوصول للشبكة"""
    print("🔧 إعداد الوصول للشبكة...")
    
    # التحقق من توفر المنفذ
    if not check_port_availability(port):
        print(f"❌ المنفذ {port} مستخدم بالفعل")
        return False
    
    # محاولة إعداد جدار الحماية في Windows
    if platform.system() == "Windows":
        print("🛡️  محاولة إعداد جدار الحماية...")
        if configure_firewall_windows(port):
            print("✅ تم إعداد جدار الحماية بنجاح")
        else:
            print("⚠️  لم يتم إعداد جدار الحماية تلقائياً")
            print("   يرجى فتح المنفذ يدوياً إذا لزم الأمر")
    
    return True

if __name__ == "__main__":
    print_network_info()
