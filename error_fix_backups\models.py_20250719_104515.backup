from flask_login import UserMixin
from datetime import datetime
from decimal import Decimal
from sqlalchemy import Numeric

def create_models(db):
    
    # جدول ربط المستخدمين بالصلاحيات (Many-to-Many)
    user_permissions = db.<PERSON>('user_permissions',
        db.Column('user_id', db.<PERSON><PERSON>, db.<PERSON><PERSON>('users.id'), primary_key=True),
        db.<PERSON>umn('permission_id', db.<PERSON><PERSON><PERSON>, db.<PERSON>('permissions.id'), primary_key=True)
    )

    # جدول ربط الأدوار بالصلاحيات (Many-to-Many)
    role_permissions = db.Table('role_permissions',
        db.Column('role_id', db.Integer, db.<PERSON>ey('roles.id'), primary_key=True),
        db.Column('permission_id', db.Inte<PERSON>, db.<PERSON>ey('permissions.id'), primary_key=True)
    )

    class Permission(db.Model):
        __tablename__ = 'permissions'

        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(100), unique=True, nullable=False)  # اسم الصلاحية بالإنجليزية
        display_name = db.Column(db.String(200), nullable=False)  # اسم الصلاحية بالعربية
        description = db.Column(db.Text)  # وصف الصلاحية
        category = db.Column(db.String(50))  # فئة الصلاحية (users, cases, clients, etc.)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        is_active = db.Column(db.Boolean, default=True)

    class Role(db.Model):
        __tablename__ = 'roles'

        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(50), unique=True, nullable=False)  # اسم الدور بالإنجليزية
        display_name = db.Column(db.String(100), nullable=False)  # اسم الدور بالعربية
        description = db.Column(db.Text)  # وصف الدور
        is_default = db.Column(db.Boolean, default=False)  # هل هو دور افتراضي
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        is_active = db.Column(db.Boolean, default=True)

        # العلاقات
        permissions = db.relationship('Permission', secondary=role_permissions, backref='roles')
        users = db.relationship('User', backref='user_role')

    class User(UserMixin, db.Model):
        __tablename__ = 'users'

        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        password_hash = db.Column(db.String(255), nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        phone = db.Column(db.String(20))
        role = db.Column(db.String(50), default='user')  # للتوافق مع النظام القديم
        role_id = db.Column(db.Integer, db.ForeignKey('roles.id'))  # الدور الجديد
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        last_login = db.Column(db.DateTime)
        is_active = db.Column(db.Boolean, default=True)

        # العلاقات
        lawyer_profile = db.relationship('Lawyer', backref='user', uselist=False)
        uploaded_documents = db.relationship('Document', backref='uploader')
        case_notes = db.relationship('CaseNote', backref='author')
        timeline_events = db.relationship('CaseTimeline', backref='creator')
        permissions = db.relationship('Permission', secondary=user_permissions, backref='users')

        def has_permission(self, permission_name):
            """التحقق من وجود صلاحية معينة للمستخدم"""
            # التحقق من الصلاحيات المباشرة
            for permission in self.permissions:
                if permission.name == permission_name and permission.is_active:
                    return True

            # التحقق من صلاحيات الدور
            if self.user_role:
                for permission in self.user_role.permissions:
                    if permission.name == permission_name and permission.is_active:
                        return True

            return False

        def has_any_permission(self, permission_names):
            """التحقق من وجود أي من الصلاحيات المحددة"""
            for permission_name in permission_names:
                if self.has_permission(permission_name):
                    return True
            return False

        def get_all_permissions(self):
            """الحصول على جميع صلاحيات المستخدم"""
            permissions = set()

            # إضافة الصلاحيات المباشرة
            for permission in self.permissions:
                if permission.is_active:
                    permissions.add(permission.name)

            # إضافة صلاحيات الدور
            if self.user_role:
                for permission in self.user_role.permissions:
                    if permission.is_active:
                        permissions.add(permission.name)

            return list(permissions)

    class Client(db.Model):
        __tablename__ = 'clients'

        id = db.Column(db.Integer, primary_key=True)
        client_code = db.Column(db.String(50), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        email = db.Column(db.String(120))
        phone = db.Column(db.String(20))
        address = db.Column(db.Text)
        national_id = db.Column(db.String(50))
        client_type = db.Column(db.String(50))  # individual, company, organization
        notes = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        is_active = db.Column(db.Boolean, default=True)

        # العلاقات
        cases = db.relationship('Case', backref='client')
        appointments = db.relationship('Appointment', backref='client')
        invoices = db.relationship('Invoice', backref='client')
        client_files = db.relationship('ClientFile', backref='client', cascade='all, delete-orphan')

    class ClientFile(db.Model):
        __tablename__ = 'client_files'

        id = db.Column(db.Integer, primary_key=True)
        client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
        filename = db.Column(db.String(255), nullable=False)
        original_filename = db.Column(db.String(255), nullable=False)
        file_path = db.Column(db.String(500), nullable=False)
        file_size = db.Column(db.Integer)
        file_type = db.Column(db.String(100))
        description = db.Column(db.Text)
        uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'))
        created_at = db.Column(db.DateTime, default=datetime.utcnow)

        # العلاقات
        uploader = db.relationship('User', backref='uploaded_client_files')

    class Lawyer(db.Model):
        __tablename__ = 'lawyers'
        
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
        license_number = db.Column(db.String(100), unique=True, nullable=False)
        specialization = db.Column(db.String(200))
        hourly_rate = db.Column(Numeric(10, 2))
        bio = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        is_active = db.Column(db.Boolean, default=True)
        
        # العلاقات
        cases = db.relationship('Case', backref='lawyer')
        appointments = db.relationship('Appointment', backref='lawyer')

    class Case(db.Model):
        __tablename__ = 'cases'
        
        id = db.Column(db.Integer, primary_key=True)
        case_number = db.Column(db.String(100), unique=True, nullable=False)
        case_title = db.Column(db.String(300), nullable=False)
        client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
        lawyer_id = db.Column(db.Integer, db.ForeignKey('lawyers.id'), nullable=False)
        case_type = db.Column(db.String(100))  # civil, criminal, commercial, etc.
        case_status = db.Column(db.String(50), default='pending')  # pending, active, closed, won, lost
        court_name = db.Column(db.String(200))
        case_category = db.Column(db.String(100))
        case_value = db.Column(Numeric(15, 2))
        filing_date = db.Column(db.Date)
        next_hearing_date = db.Column(db.Date)
        case_description = db.Column(db.Text)
        notes = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        is_active = db.Column(db.Boolean, default=True)
        
        # العلاقات
        sessions = db.relationship('CaseSession', backref='case')
        documents = db.relationship('Document', backref='case')
        invoices = db.relationship('Invoice', backref='case')
        notes_list = db.relationship('CaseNote', backref='case')
        timeline = db.relationship('CaseTimeline', backref='case')
        appointments = db.relationship('Appointment', backref='case')

    class CaseSession(db.Model):
        __tablename__ = 'case_sessions'
        
        id = db.Column(db.Integer, primary_key=True)
        case_id = db.Column(db.Integer, db.ForeignKey('cases.id'), nullable=False)
        session_type = db.Column(db.String(100))  # hearing, consultation, mediation
        session_date = db.Column(db.DateTime, nullable=False)
        court_room = db.Column(db.String(100))
        session_status = db.Column(db.String(50), default='scheduled')  # scheduled, completed, postponed, cancelled
        session_notes = db.Column(db.Text)
        session_result = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    class Appointment(db.Model):
        __tablename__ = 'appointments'
        
        id = db.Column(db.Integer, primary_key=True)
        client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
        lawyer_id = db.Column(db.Integer, db.ForeignKey('lawyers.id'), nullable=False)
        case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
        appointment_type = db.Column(db.String(100))  # consultation, meeting, court_hearing
        appointment_date = db.Column(db.DateTime, nullable=False)
        status = db.Column(db.String(50), default='scheduled')  # scheduled, completed, cancelled, no_show
        notes = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    class Document(db.Model):
        __tablename__ = 'documents'
        
        id = db.Column(db.Integer, primary_key=True)
        case_id = db.Column(db.Integer, db.ForeignKey('cases.id'), nullable=False)
        document_name = db.Column(db.String(300), nullable=False)
        document_type = db.Column(db.String(100))  # contract, evidence, court_order, etc.
        file_path = db.Column(db.String(500), nullable=False)
        file_size = db.Column(db.String(50))
        mime_type = db.Column(db.String(100))
        description = db.Column(db.Text)
        upload_date = db.Column(db.DateTime, default=datetime.utcnow)
        uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
        is_active = db.Column(db.Boolean, default=True)

    class Invoice(db.Model):
        __tablename__ = 'invoices'
        
        id = db.Column(db.Integer, primary_key=True)
        invoice_number = db.Column(db.String(100), unique=True, nullable=False)
        client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
        case_id = db.Column(db.Integer, db.ForeignKey('cases.id'))
        amount = db.Column(Numeric(15, 2), nullable=False)
        tax_amount = db.Column(Numeric(15, 2), default=0)
        total_amount = db.Column(Numeric(15, 2), nullable=False)
        invoice_status = db.Column(db.String(50), default='pending')  # pending, paid, overdue, cancelled
        issue_date = db.Column(db.Date, default=datetime.utcnow)
        due_date = db.Column(db.Date)
        payment_date = db.Column(db.Date)
        description = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        
        # العلاقات
        payments = db.relationship('Payment', backref='invoice')

    class Payment(db.Model):
        __tablename__ = 'payments'
        
        id = db.Column(db.Integer, primary_key=True)
        invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
        amount = db.Column(Numeric(15, 2), nullable=False)
        payment_method = db.Column(db.String(50))  # cash, bank_transfer, check, credit_card
        payment_status = db.Column(db.String(50), default='completed')  # completed, pending, failed
        payment_date = db.Column(db.Date, default=datetime.utcnow)
        reference_number = db.Column(db.String(100))
        notes = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)

    class CaseNote(db.Model):
        __tablename__ = 'case_notes'
        
        id = db.Column(db.Integer, primary_key=True)
        case_id = db.Column(db.Integer, db.ForeignKey('cases.id'), nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
        note_type = db.Column(db.String(50))  # general, important, reminder, update
        note_content = db.Column(db.Text, nullable=False)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    class CaseTimeline(db.Model):
        __tablename__ = 'case_timeline'
        
        id = db.Column(db.Integer, primary_key=True)
        case_id = db.Column(db.Integer, db.ForeignKey('cases.id'), nullable=False)
        event_type = db.Column(db.String(100))  # case_created, hearing_scheduled, document_uploaded, etc.
        event_title = db.Column(db.String(200), nullable=False)
        event_description = db.Column(db.Text)
        event_date = db.Column(db.DateTime, default=datetime.utcnow)
        created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)

    class Contract(db.Model):
        __tablename__ = 'contracts'

        id = db.Column(db.Integer, primary_key=True)
        contract_number = db.Column(db.String(50), unique=True, nullable=False)
        title = db.Column(db.String(200), nullable=False)
        title_en = db.Column(db.String(200))  # العنوان بالإنجليزية

        # بيانات العميل
        client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
        client_name = db.Column(db.String(200), nullable=False)  # نسخة للأرشفة
        client_email = db.Column(db.String(120))
        client_phone = db.Column(db.String(20))
        client_address = db.Column(db.Text)

        # بيانات العقد
        contract_type = db.Column(db.String(100), nullable=False)  # نوع العقد
        contract_type_en = db.Column(db.String(100))  # نوع العقد بالإنجليزية
        description = db.Column(db.Text)  # وصف العقد
        description_en = db.Column(db.Text)  # وصف العقد بالإنجليزية

        # بنود العقد
        terms_and_conditions = db.Column(db.Text)  # البنود والشروط
        terms_and_conditions_en = db.Column(db.Text)  # البنود والشروط بالإنجليزية

        # المدة والتواريخ
        start_date = db.Column(db.Date, nullable=False)
        end_date = db.Column(db.Date)
        duration_months = db.Column(db.Integer)  # المدة بالأشهر

        # المبالغ المالية
        total_amount = db.Column(Numeric(15, 2), nullable=False)
        currency = db.Column(db.String(10), default='SAR')
        payment_terms = db.Column(db.Text)  # شروط الدفع
        payment_terms_en = db.Column(db.Text)  # شروط الدفع بالإنجليزية

        # الحالة والمتابعة
        status = db.Column(db.String(50), default='draft')  # draft, active, completed, cancelled, expired
        priority = db.Column(db.String(20), default='medium')  # low, medium, high

        # الملفات والمستندات
        pdf_file_path = db.Column(db.String(500))  # مسار ملف PDF
        original_filename = db.Column(db.String(200))  # اسم الملف الأصلي
        file_size = db.Column(db.Integer)  # حجم الملف بالبايت

        # بيانات إضافية
        notes = db.Column(db.Text)  # ملاحظات
        notes_en = db.Column(db.Text)  # ملاحظات بالإنجليزية
        tags = db.Column(db.String(500))  # علامات للبحث

        # المستخدم المسؤول
        assigned_lawyer_id = db.Column(db.Integer, db.ForeignKey('lawyers.id'))
        created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

        # التواريخ
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        signed_at = db.Column(db.DateTime)  # تاريخ التوقيع

        # العلاقات
        client = db.relationship('Client', backref='contracts')
        assigned_lawyer = db.relationship('Lawyer', backref='assigned_contracts')
        created_by = db.relationship('User', backref='created_contracts')

        def __repr__(self):
            return f'<Contract {self.contract_number}: {self.title}>'

        @property
        def status_display(self):
            """عرض الحالة بالعربية"""
            status_map = {
                'draft': 'مسودة',
                'active': 'نشط',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'expired': 'منتهي الصلاحية'
            }
            return status_map.get(self.status, self.status)

        @property
        def priority_display(self):
            """عرض الأولوية بالعربية"""
            priority_map = {
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية'
            }
            return priority_map.get(self.priority, self.priority)

        @property
        def is_expired(self):
            """التحقق من انتهاء صلاحية العقد"""
            if self.end_date:
                return datetime.now().date() > self.end_date
            return False

        @property
        def days_remaining(self):
            """عدد الأيام المتبقية"""
            if self.end_date:
                delta = self.end_date - datetime.now().date()
                return delta.days if delta.days > 0 else 0
            return None

        def get_file_url(self):
            """الحصول على رابط الملف"""
            if self.pdf_file_path:
                return f'/contracts/download/{self.id}'
            return None

    class Department(db.Model):
        __tablename__ = 'departments'

        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(100), nullable=False)
        name_en = db.Column(db.String(100))
        description = db.Column(db.Text)
        manager_id = db.Column(db.Integer, db.ForeignKey('employees.id'))
        budget = db.Column(Numeric(12, 2))
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

        # العلاقات
        employees = db.relationship('Employee', backref='department', foreign_keys='Employee.department_id')

    class Employee(db.Model):
        __tablename__ = 'employees'

        id = db.Column(db.Integer, primary_key=True)
        employee_number = db.Column(db.String(50), unique=True, nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('users.id'))

        # البيانات الشخصية
        full_name = db.Column(db.String(200), nullable=False)
        full_name_en = db.Column(db.String(200))
        national_id = db.Column(db.String(50), unique=True)
        passport_number = db.Column(db.String(50))
        email = db.Column(db.String(120))
        phone = db.Column(db.String(20))
        mobile = db.Column(db.String(20))
        address = db.Column(db.Text)
        birth_date = db.Column(db.Date)
        gender = db.Column(db.String(10))  # male, female
        nationality = db.Column(db.String(50))
        marital_status = db.Column(db.String(20))  # single, married, divorced, widowed

        # البيانات الوظيفية
        department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))
        position = db.Column(db.String(100))
        position_en = db.Column(db.String(100))
        employment_type = db.Column(db.String(50))  # full_time, part_time, contract, intern
        employment_status = db.Column(db.String(50))  # active, inactive, terminated, resigned
        hire_date = db.Column(db.Date)
        termination_date = db.Column(db.Date)

        # البيانات المالية
        basic_salary = db.Column(Numeric(10, 2))
        allowances = db.Column(Numeric(10, 2))
        deductions = db.Column(Numeric(10, 2), default=0.0)
        total_salary = db.Column(Numeric(10, 2))
        bank_account = db.Column(db.String(50))
        bank_name = db.Column(db.String(100))

        # إعدادات النظام
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

        # العلاقات
        user = db.relationship('User', foreign_keys=[user_id], backref='employee_profile')
        creator = db.relationship('User', foreign_keys=[created_by])
        attendances = db.relationship('Attendance', back_populates='employee', cascade='all, delete-orphan')
        penalties = db.relationship('Penalty', back_populates='employee', cascade='all, delete-orphan')
        warnings = db.relationship('Warning', back_populates='employee', cascade='all, delete-orphan')
        leave_requests = db.relationship('LeaveRequest', back_populates='employee', cascade='all, delete-orphan')
        documents = db.relationship('EmployeeDocument', backref='employee', cascade='all, delete-orphan')





    class EmployeeDocument(db.Model):
        __tablename__ = 'employee_documents'

        id = db.Column(db.Integer, primary_key=True)
        employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)

        # معلومات الملف
        filename = db.Column(db.String(255), nullable=False)
        original_filename = db.Column(db.String(255), nullable=False)
        file_path = db.Column(db.String(500), nullable=False)
        file_size = db.Column(db.Integer)
        file_type = db.Column(db.String(100))

        # تصنيف المستند
        document_type = db.Column(db.String(100), nullable=False)  # contract, certificate, medical, id_copy, etc.
        document_category = db.Column(db.String(100))  # personal, employment, medical, legal
        title = db.Column(db.String(200))
        description = db.Column(db.Text)

        # التواريخ
        document_date = db.Column(db.Date)  # تاريخ المستند
        expiry_date = db.Column(db.Date)  # تاريخ انتهاء الصلاحية

        # الحالة
        is_confidential = db.Column(db.Boolean, default=False)
        is_active = db.Column(db.Boolean, default=True)

        # معلومات النظام
        uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

        # العلاقات
        uploader = db.relationship('User', backref='uploaded_employee_documents')

    class Attendance(db.Model):
        __tablename__ = 'attendance'
        __table_args__ = {'extend_existing': True}

        id = db.Column(db.Integer, primary_key=True)
        employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
        date = db.Column(db.Date, nullable=False)
        check_in = db.Column(db.Time)
        check_out = db.Column(db.Time)
        break_start = db.Column(db.Time)
        break_end = db.Column(db.Time)
        hours_worked = db.Column(Numeric(4, 2))
        overtime_hours = db.Column(Numeric(4, 2), default=0.0)
        status = db.Column(db.String(20), default='present')  # present, absent, late, half_day
        notes = db.Column(db.Text)
        location = db.Column(db.String(100))
        ip_address = db.Column(db.String(45))
        is_manual = db.Column(db.Boolean, default=False)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

        # العلاقات
        employee = db.relationship('Employee', back_populates='attendances')
        creator = db.relationship('User', backref='created_attendance')

    class LeaveRequest(db.Model):
        __tablename__ = 'leave_requests'
        __table_args__ = {'extend_existing': True}

        id = db.Column(db.Integer, primary_key=True)
        employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
        leave_type = db.Column(db.String(50), nullable=False)  # annual, sick, emergency, maternity, etc.
        start_date = db.Column(db.Date, nullable=False)
        end_date = db.Column(db.Date, nullable=False)
        days_count = db.Column(db.Integer, nullable=False)
        reason = db.Column(db.Text, nullable=False)
        status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
        approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))
        approval_date = db.Column(db.DateTime)
        approval_notes = db.Column(db.Text)
        attachment_path = db.Column(db.String(255))
        is_paid = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

        # العلاقات
        employee = db.relationship('Employee', back_populates='leave_requests')
        approver = db.relationship('User', backref='approved_leaves')

    class Warning(db.Model):
        __tablename__ = 'warnings'
        __table_args__ = {'extend_existing': True}

        id = db.Column(db.Integer, primary_key=True)
        employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
        warning_type = db.Column(db.String(50), nullable=False)  # verbal, written, final
        category = db.Column(db.String(50), nullable=False)  # attendance, performance, conduct, etc.
        title = db.Column(db.String(200), nullable=False)
        description = db.Column(db.Text, nullable=False)
        incident_date = db.Column(db.Date, nullable=False)
        severity = db.Column(db.String(20), default='medium')  # low, medium, high, critical
        action_required = db.Column(db.Text)
        deadline = db.Column(db.Date)
        status = db.Column(db.String(20), default='active')  # active, resolved, expired
        follow_up_date = db.Column(db.Date)
        resolution_notes = db.Column(db.Text)
        attachment_path = db.Column(db.String(255))
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

        # العلاقات
        employee = db.relationship('Employee', back_populates='warnings')
        creator = db.relationship('User', backref='created_warnings')

    class Penalty(db.Model):
        __tablename__ = 'penalties'
        __table_args__ = {'extend_existing': True}

        id = db.Column(db.Integer, primary_key=True)
        employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
        penalty_type = db.Column(db.String(50), nullable=False)  # financial, suspension, termination, etc.
        category = db.Column(db.String(50), nullable=False)  # attendance, performance, conduct, etc.
        title = db.Column(db.String(200), nullable=False)
        description = db.Column(db.Text, nullable=False)
        incident_date = db.Column(db.Date, nullable=False)
        penalty_date = db.Column(db.Date, nullable=False)
        amount = db.Column(Numeric(10, 2))  # للجزاءات المالية
        days_count = db.Column(db.Integer)  # لأيام الإيقاف
        severity = db.Column(db.String(20), default='medium')  # low, medium, high, critical
        status = db.Column(db.String(20), default='active')  # active, completed, cancelled
        payment_status = db.Column(db.String(20), default='pending')  # pending, paid, waived
        notes = db.Column(db.Text)
        attachment_path = db.Column(db.String(255))
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

        # العلاقات
        employee = db.relationship('Employee', back_populates='penalties')
        creator = db.relationship('User', backref='created_penalties')

    class SystemSettings(db.Model):
        __tablename__ = 'system_settings'

        id = db.Column(db.Integer, primary_key=True)
        setting_key = db.Column(db.String(100), unique=True, nullable=False)
        setting_value = db.Column(db.Text)
        setting_type = db.Column(db.String(50), default='text')  # text, file, boolean, number
        category = db.Column(db.String(50), default='general')  # general, company, reports, system
        display_name = db.Column(db.String(200))
        description = db.Column(db.Text)
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

        # العلاقات
        updater = db.relationship('User', backref='updated_settings')

        @staticmethod
        def get_setting(key, default=None):
            """الحصول على قيمة إعداد"""
            setting = SystemSettings.query.filter_by(setting_key=key, is_active=True).first()
            return setting.setting_value if setting else default

        @staticmethod
        def set_setting(key, value, setting_type='text', category='general', display_name=None, description=None, user_id=None):
            """تعيين قيمة إعداد"""
            setting = SystemSettings.query.filter_by(setting_key=key).first()
            if setting:
                setting.setting_value = value
                setting.setting_type = setting_type
                setting.category = category
                setting.display_name = display_name or setting.display_name
                setting.description = description or setting.description
                setting.updated_at = datetime.utcnow()
                setting.updated_by = user_id
            else:
                setting = SystemSettings(
                    setting_key=key,
                    setting_value=value,
                    setting_type=setting_type,
                    category=category,
                    display_name=display_name or key,
                    description=description,
                    updated_by=user_id
                )
                db.session.add(setting)
            return setting

    return {
        'User': User,
        'Role': Role,
        'Permission': Permission,
        'Client': Client,
        'ClientFile': ClientFile,
        'Lawyer': Lawyer,
        'Case': Case,
        'CaseSession': CaseSession,
        'Appointment': Appointment,
        'Document': Document,
        'Invoice': Invoice,
        'Payment': Payment,
        'CaseNote': CaseNote,
        'CaseTimeline': CaseTimeline,
        'Contract': Contract,
        'Department': Department,
        'Employee': Employee,
        'Attendance': Attendance,
        'Penalty': Penalty,
        'Warning': Warning,
        'EmployeeDocument': EmployeeDocument,
        'LeaveRequest': LeaveRequest,
        'SystemSettings': SystemSettings,
        'user_permissions': user_permissions,
        'role_permissions': role_permissions
    }
