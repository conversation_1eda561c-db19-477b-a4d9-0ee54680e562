{% extends "base.html" %}

{% block title %}الإنذارات{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-exclamation-triangle"></i>
            إدارة الإنذارات
          </h3>
          <div class="card-tools">
            <a href="{{ {% url 'warnings.add' %} }}" class="btn btn-primary btn-sm">
              <i class="fas fa-plus"></i>
              إضافة إنذار جديد
            </a>
            <a href="{{ {% url 'warnings.report' %} }}" class="btn btn-info btn-sm">
              <i class="fas fa-chart-bar"></i>
              تقرير الإنذارات
            </a>
          </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="info-box bg-info">
                <span class="info-box-icon"><i class="fas fa-list"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">إجمالي الإنذارات</span>
                  <span class="info-box-number">{{ stats.total_warnings }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-warning">
                <span class="info-box-icon"><i class="fas fa-exclamation"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">نشطة</span>
                  <span class="info-box-number">{{ stats.active_warnings }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-success">
                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">تم حلها</span>
                  <span class="info-box-number">{{ stats.resolved_warnings }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-danger">
                <span class="info-box-icon"><i class="fas fa-exclamation-triangle"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">حرجة</span>
                  <span class="info-box-number">{{ stats.critical_warnings }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-primary">
                <span class="info-box-icon"><i class="fas fa-calendar"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">هذا الشهر</span>
                  <span class="info-box-number">{{ stats.this_month }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- فلاتر البحث -->
          <div class="card card-outline card-primary collapsed-card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-filter"></i>
                فلاتر البحث
              </h3>
              <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
            <div class="card-body">
              <form method="GET" action="{{ {% url 'warnings.index' %} }}">
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="search">البحث</label>
                      <input type="text" class="form-control" id="search" name="search" 
                             value="{{ search }}" placeholder="اسم الموظف أو عنوان الإنذار...">
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="employee_id">الموظف</label>
                      <select class="form-control" id="employee_id" name="employee_id">
                        <option value="">جميع الموظفين</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" 
                                {% if employee_id == employee.id %}selected{% endif %}>
                          {{ employee.full_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="warning_type">نوع الإنذار</label>
                      <select class="form-control" id="warning_type" name="warning_type">
                        <option value="">جميع الأنواع</option>
                        {% for type_code, type_name in warning_types %}
                        <option value="{{ type_code }}" {% if warning_type == type_code %}selected{% endif %}>
                          {{ type_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="severity">الخطورة</label>
                      <select class="form-control" id="severity" name="severity">
                        <option value="">جميع المستويات</option>
                        {% for severity_code, severity_name in severities %}
                        <option value="{{ severity_code }}" {% if severity == severity_code %}selected{% endif %}>
                          {{ severity_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="status">الحالة</label>
                      <select class="form-control" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        {% for status_code, status_name in statuses %}
                        <option value="{{ status_code }}" {% if status == status_code %}selected{% endif %}>
                          {{ status_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                      <i class="fas fa-search"></i>
                      بحث
                    </button>
                    <a href="{{ {% url 'warnings.index' %} }}" class="btn btn-secondary">
                      <i class="fas fa-undo"></i>
                      إعادة تعيين
                    </a>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <!-- جدول الإنذارات -->
          <div class="table-responsive">
            <table class="table table-bordered table-striped">
              <thead>
                <tr>
                  <th>الموظف</th>
                  <th>نوع الإنذار</th>
                  <th>العنوان</th>
                  <th>الفئة</th>
                  <th>تاريخ الحادثة</th>
                  <th>الخطورة</th>
                  <th>الحالة</th>
                  <th>الموعد النهائي</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {% for warning in warnings %}
                <tr>
                  <td>
                    <strong>{{ warning.employee.full_name }}</strong><br>
                    <small class="text-muted">{{ warning.employee.employee_number }}</small>
                  </td>
                  <td>
                    {% for type_code, type_name in warning_types %}
                      {% if warning.warning_type == type_code %}
                        <span class="badge badge-info">{{ type_name }}</span>
                        
                      {% endif %}
                    {% endfor %}
                  </td>
                  <td>
                    <strong>{{ warning.title }}</strong><br>
                    <small class="text-muted">{{ warning.description|truncatechars:50 }}{% if warning.description|length > 50 %}...{% endif %}</small>
                  </td>
                  <td>
                    {% for cat_code, cat_name in categories %}
                      {% if warning.category == cat_code %}
                        <small>{{ cat_name }}</small>
                        
                      {% endif %}
                    {% endfor %}
                  </td>
                  <td>{{ warning.incident_date|date:"%Y-%m-%d" }}</td>
                  <td>
                    {% if warning.severity == 'low' %}
                      <span class="badge badge-success">منخفض</span>
                    {% elif warning.severity == 'medium' %}
                      <span class="badge badge-warning">متوسط</span>
                    {% elif warning.severity == 'high' %}
                      <span class="badge badge-danger">عالي</span>
                    {% elif warning.severity == 'critical' %}
                      <span class="badge badge-dark">حرج</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if warning.status == 'active' %}
                      <span class="badge badge-warning">نشط</span>
                    {% elif warning.status == 'resolved' %}
                      <span class="badge badge-success">تم الحل</span>
                    {% elif warning.status == 'expired' %}
                      <span class="badge badge-secondary">منتهي</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if warning.deadline %}
                      {{ warning.deadline|date:"%Y-%m-%d" }}
                      {% if warning.deadline < today and warning.status == 'active' %}
                        <br><small class="text-danger">متأخر</small>
                      {% endif %}
                    {% else %}
                      <span class="text-muted">-</span>
                    {% endif %}
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <a href="{{ url_for('warnings.edit', warning_id=warning.id) }}" 
                         class="btn btn-warning btn-sm" title="تعديل">
                        <i class="fas fa-edit"></i>
                      </a>
                      <button type="button" class="btn btn-info btn-sm"
                              onclick="viewWarning('{{ warning.id }}')" title="عرض">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button type="button" class="btn btn-danger btn-sm"
                              onclick="deleteWarning('{{ warning.id }}')" title="حذف">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                {% else %}
                <tr>
                  <td colspan="9" class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    لا توجد إنذارات
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- نافذة عرض تفاصيل الإنذار -->
<div class="modal fade" id="viewWarningModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">تفاصيل الإنذار</h4>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body" id="warningDetails">
        <!-- سيتم تحميل التفاصيل هنا -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

<script>
// عرض تفاصيل الإنذار
function viewWarning(id) {
  // هنا يمكن إضافة استدعاء AJAX لجلب تفاصيل الإنذار
  const modal = new bootstrap.Modal(document.getElementById('viewWarningModal'));
  modal.show();
}

// حذف الإنذار
function deleteWarning(id) {
  if (confirm('هل أنت متأكد من حذف الإنذار؟\nلا يمكن التراجع عن هذا الإجراء.')) {
    fetch(`/warnings/${id}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('خطأ: ' + data.message);
      }
    })
    .catch(error => {
      alert('حدث خطأ في الاتصال');
    });
  }
}

// تحسين عرض الجدول
document.addEventListener('DOMContentLoaded', function() {
  // إضافة ألوان للصفوف حسب الخطورة
  const rows = document.querySelectorAll('tbody tr');
  rows.forEach(row => {
    const severityBadge = row.querySelector('.badge');
    if (severityBadge) {
      if (severityBadge.textContent.includes('حرج')) {
        row.style.backgroundColor = '#f8d7da';
      } else if (severityBadge.textContent.includes('عالي')) {
        row.style.backgroundColor = '#fff3cd';
      }
    }
  });
});
</script>
{% endblock %}
