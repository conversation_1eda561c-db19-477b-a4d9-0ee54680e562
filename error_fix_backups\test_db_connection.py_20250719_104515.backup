#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار اتصال قاعدة البيانات
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from flask import Flask
    from flask_sqlalchemy import SQLAlchemy
    from dotenv import load_dotenv
    
    # تحميل متغيرات البيئة
    load_dotenv()
    
    # إنشاء تطبيق Flask
    app = Flask(__name__)
    
    # إعدادات التطبيق
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///legal_system.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    print(f"🔍 DATABASE_URL من متغيرات البيئة: {os.environ.get('DATABASE_URL', 'غير محدد')}")
    print(f"🔍 SQLALCHEMY_DATABASE_URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
    
    # تهيئة قاعدة البيانات
    db = SQLAlchemy(app)
    
    with app.app_context():
        # اختبار الاتصال
        try:
            # تنفيذ استعلام بسيط
            with db.engine.connect() as connection:
                result = connection.execute(db.text("SELECT name FROM sqlite_master WHERE type='table'"))
                tables = [row[0] for row in result]

                print(f"✅ تم الاتصال بقاعدة البيانات بنجاح")
                print(f"📋 الجداول الموجودة: {', '.join(tables)}")

                # التحقق من جدول الجزاءات
                if 'penalties' in tables:
                    print("✅ جدول الجزاءات موجود")

                    # فحص أعمدة جدول الجزاءات
                    result = connection.execute(db.text("PRAGMA table_info(penalties)"))
                    columns = [row[1] for row in result]
                    print(f"📋 أعمدة جدول الجزاءات: {', '.join(columns)}")

                    if 'category' in columns:
                        print("✅ عمود category موجود")
                    else:
                        print("❌ عمود category مفقود")

                    # عدد السجلات
                    result = connection.execute(db.text("SELECT COUNT(*) FROM penalties"))
                    count = result.fetchone()[0]
                    print(f"📊 عدد سجلات الجزاءات: {count}")

                else:
                    print("❌ جدول الجزاءات غير موجود")
                
        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
except Exception as e:
    print(f"❌ خطأ عام: {e}")
