# دليل إعداد خادم نظام الشؤون القانونية

## نظرة عامة
هذا الدليل يوضح كيفية إعداد وتكوين خادم نظام الشؤون القانونية للعمل على شبكة محلية أو خارجية.

## متطلبات النظام
- Python 3.7 أو أحدث
- Flask وجميع المكتبات المطلوبة (انظر requirements.txt)
- نظام تشغيل Windows/Linux/macOS

## التشغيل السريع

### 1. تشغيل النظام
```bash
python app.py
```

### 2. الوصول للنظام
- **محلياً**: http://localhost:5000
- **من الشبكة**: http://[IP-ADDRESS]:5000

### 3. بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## إعداد الخادم من واجهة النظام

### الوصول لإعدادات الخادم
1. سجل دخول كمدير
2. اذهب إلى **لوحة التحكم** → **إعدادات النظام**
3. اختر تبويب **إعدادات النظام**
4. ستجد قسم **معلومات الشبكة الحالية** و **إعدادات الخادم**

### معلومات الشبكة الحالية
يعرض هذا القسم:
- عنوان IP المحلي الحالي
- المنفذ المستخدم
- روابط الوصول (محلي ومن الشبكة)
- حالة الأمان (HTTP/HTTPS)
- وضع التشغيل (تطوير/إنتاج)

### إعدادات الخادم القابلة للتعديل

#### الإعدادات الأساسية
- **عنوان الخادم (Host)**:
  - `0.0.0.0`: للوصول من جميع الشبكات
  - `127.0.0.1`: للوصول المحلي فقط
  - `192.168.x.x`: لعنوان IP محدد

- **منفذ الخادم (Port)**: 
  - القيمة الافتراضية: 5000
  - يمكن تغييرها لأي منفذ متاح (1-65535)

- **وضع التطوير (Debug Mode)**:
  - مفعل: لعرض تفاصيل الأخطاء (للتطوير)
  - معطل: للبيئة الإنتاجية

#### إعدادات الشبكة
- **السماح بالوصول الخارجي**: تمكين الأجهزة الأخرى من الوصول
- **الحد الأقصى للاتصالات**: عدد المستخدمين المتزامنين
- **مهلة الاتصال**: المدة المسموحة قبل انتهاء الجلسة

#### إعدادات الأمان
- **تفعيل HTTPS**: يتطلب شهادة SSL
- **مسار شهادة SSL**: مسار ملف الشهادة
- **مسار مفتاح SSL**: مسار ملف المفتاح الخاص

#### إعدادات الأداء
- **التخزين المؤقت**: لتحسين سرعة النظام
- **ضغط البيانات**: لتقليل استهلاك الشبكة
- **مدة التخزين المؤقت**: بالثواني

## إعداد الشبكة يدوياً

### ملف server_config.json
يمكن تعديل إعدادات الخادم مباشرة من خلال ملف `server_config.json`:

```json
{
  "host": "0.0.0.0",
  "port": 5000,
  "debug": true,
  "threaded": true,
  "use_reloader": false,
  "description": "إعدادات خادم نظام الشؤون القانونية",
  "network_settings": {
    "allow_external_access": true,
    "max_connections": 100,
    "timeout": 30
  },
  "security_settings": {
    "enable_https": false,
    "ssl_cert_path": "",
    "ssl_key_path": ""
  },
  "performance_settings": {
    "enable_caching": true,
    "cache_timeout": 300,
    "enable_compression": true
  }
}
```

## إعداد جدار الحماية

### Windows
```cmd
netsh advfirewall firewall add rule name="Legal_System" dir=in action=allow protocol=TCP localport=5000
```

### Linux (Ubuntu/Debian)
```bash
sudo ufw allow 5000
```

### Linux (CentOS/RHEL)
```bash
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload
```

## استكشاف الأخطاء

### المشاكل الشائعة

1. **لا يمكن الوصول من الشبكة**:
   - تأكد من أن Host مضبوط على `0.0.0.0`
   - تحقق من إعدادات جدار الحماية
   - تأكد من أن الأجهزة على نفس الشبكة

2. **المنفذ مستخدم**:
   - غير المنفذ في الإعدادات
   - أو أوقف البرنامج الذي يستخدم المنفذ

3. **بطء في الأداء**:
   - فعل التخزين المؤقت
   - فعل ضغط البيانات
   - قلل عدد الاتصالات المتزامنة

### سجلات النظام
- تحقق من سجلات النظام في قسم **السجلات** في لوحة التحكم
- راجع رسائل الخطأ في terminal/command prompt

## الأمان

### للبيئة الإنتاجية
1. **عطل وضع التطوير** (Debug Mode)
2. **فعل HTTPS** مع شهادة SSL صالحة
3. **غير كلمة مرور المدير** الافتراضية
4. **استخدم جدار حماية** مناسب
5. **حدث النظام** بانتظام

### شهادات SSL
لتفعيل HTTPS، تحتاج إلى:
1. شهادة SSL (.crt أو .pem)
2. مفتاح خاص (.key)
3. تحديد مسارهما في الإعدادات

## الدعم الفني
للحصول على المساعدة:
1. راجع سجلات النظام
2. تحقق من إعدادات الشبكة
3. تأكد من صحة ملفات التكوين

---

**ملاحظة**: هذا النظام مصمم للاستخدام في الشبكات المحلية الآمنة. للاستخدام على الإنترنت، يُنصح بشدة باستخدام HTTPS وإجراءات أمان إضافية.
