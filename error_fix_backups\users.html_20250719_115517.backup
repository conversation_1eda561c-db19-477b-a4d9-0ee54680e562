{% extends "base.html" %} {% block title %}إدارة المستخدمين - نظام الشؤون
القانونية{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2">
    <i class="fas fa-users me-2"></i>
    إدارة المستخدمين
  </h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
      <a href="{{ url_for('admin_add_user') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        إضافة مستخدم جديد
      </a>
      <a href="{{ url_for('admin_index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-1"></i>
        العودة للوحة التحكم
      </a>
    </div>
  </div>
</div>

<!-- البحث والفلترة -->
<div class="card mb-4">
  <div class="card-body">
    <form method="GET" class="row g-3">
      <div class="col-md-4">
        <label for="search" class="form-label">البحث</label>
        <input
          type="text"
          class="form-control"
          id="search"
          name="search"
          placeholder="الاسم، اسم المستخدم، أو البريد الإلكتروني"
        />
      </div>
      <div class="col-md-3">
        <label for="role" class="form-label">الدور</label>
        <select class="form-select" id="role" name="role">
          <option value="">جميع الأدوار</option>
          <option value="admin">مدير</option>
          <option value="lawyer">محامي</option>
          <option value="secretary">سكرتير</option>
          <option value="client">عميل</option>
        </select>
      </div>
      <div class="col-md-3">
        <label for="status" class="form-label">الحالة</label>
        <select class="form-select" id="status" name="status">
          <option value="">جميع الحالات</option>
          <option value="active">نشط</option>
          <option value="inactive">غير نشط</option>
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">&nbsp;</label>
        <div class="d-grid">
          <button type="submit" class="btn btn-outline-primary">
            <i class="fas fa-search me-1"></i>
            بحث
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- قائمة المستخدمين -->
<div class="card">
  <div class="card-header">
    <h5 class="mb-0">قائمة المستخدمين</h5>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>الصورة</th>
            <th>الاسم الكامل</th>
            <th>اسم المستخدم</th>
            <th>البريد الإلكتروني</th>
            <th>الدور</th>
            <th>الصلاحيات</th>
            <th>الحالة</th>
            <th>آخر دخول</th>
            <th>الإجراءات</th>
          </tr>
        </thead>
        <tbody>
          {% for user in users %}
          <tr>
            <td>
              <div class="avatar-sm">
                <img
                  src="https://ui-avatars.com/api/?name={{ user.full_name }}&background=007bff&color=fff&size=40"
                  class="rounded-circle"
                  alt="{{ user.full_name }}"
                />
              </div>
            </td>
            <td>
              <strong>{{ user.full_name }}</strong>
            </td>
            <td>{{ user.username }}</td>
            <td>{{ user.email }}</td>
            <td>
              {% if user.role == 'admin' %}
              <span class="badge bg-danger">مدير</span>
              {% elif user.role == 'lawyer' %}
              <span class="badge bg-primary">محامي</span>
              {% elif user.role == 'secretary' %}
              <span class="badge bg-info">سكرتير</span>
              {% else %}
              <span class="badge bg-secondary">{{ user.role }}</span>
              {% endif %}
            </td>
            <td>
              <button
                class="btn btn-outline-primary btn-sm"
                onclick="viewUserPermissions('{{ user.id }}')"
                title="عرض الصلاحيات"
              >
                <i class="fas fa-key me-1"></i>
                <span class="badge bg-info"
                  >{{ user.get('permissions_count', 0) }}</span
                >
              </button>
            </td>
            <td>
              {% if user.is_active %}
              <span class="badge bg-success">نشط</span>
              {% else %}
              <span class="badge bg-warning">غير نشط</span>
              {% endif %}
            </td>
            <td>
              {% if user.last_login %} {{ user.last_login|strftime("%Y-%m-%d
              %H:%M") }} {% else %} لم يسجل دخول {% endif %}
            </td>
            <td>
              <div class="btn-group btn-group-sm" role="group">
                <button
                  onclick="viewUser('{{ user.id }}')"
                  class="btn btn-outline-primary"
                  title="عرض"
                >
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  onclick="editUser('{{ user.id }}')"
                  class="btn btn-outline-warning"
                  title="تعديل"
                >
                  <i class="fas fa-edit"></i>
                </button>
                {% if user.id != current_user.id %}
                <button
                  onclick="toggleUserStatus('{{ user.id }}', '{{ user.is_active|lower }}')"
                  class="btn btn-outline-{% if user.is_active %}warning{% else %}success{% endif %}"
                  title="{% if user.is_active %}إلغاء تفعيل{% else %}تفعيل{% endif %}"
                >
                  <i
                    class="fas fa-{% if user.is_active %}ban{% else %}check{% endif %}"
                  ></i>
                </button>
                <button
                  onclick="deleteUser('{{ user.id }}', '{{ user.full_name }}')"
                  class="btn btn-outline-danger"
                  title="حذف"
                >
                  <i class="fas fa-trash"></i>
                </button>
                {% endif %}
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- التنقل بين الصفحات -->
    <nav aria-label="صفحات المستخدمين" class="mt-4">
      <ul class="pagination justify-content-center">
        <li class="page-item disabled">
          <span class="page-link">السابق</span>
        </li>
        <li class="page-item active">
          <span class="page-link">1</span>
        </li>
        <li class="page-item">
          <a
            class="page-link"
            href="#"
            onclick="showAlert('info', 'سيتم إضافة هذه الوظيفة قريباً')"
            >2</a
          >
        </li>
        <li class="page-item">
          <a
            class="page-link"
            href="#"
            onclick="showAlert('info', 'سيتم إضافة هذه الوظيفة قريباً')"
            >التالي</a
          >
        </li>
      </ul>
    </nav>
  </div>
</div>

<!-- نافذة عرض تفاصيل المستخدم -->
<div class="modal fade" id="userModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تفاصيل المستخدم</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body" id="userModalBody">
        <!-- سيتم تحميل المحتوى هنا -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          إغلاق
        </button>
      </div>
    </div>
  </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تأكيد الحذف</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <p>
          هل أنت متأكد من رغبتك في حذف المستخدم <strong id="userName"></strong>؟
        </p>
        <div class="alert alert-danger">
          <i class="fas fa-exclamation-triangle me-2"></i>
          تحذير: لا يمكن التراجع عن هذا الإجراء.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          إلغاء
        </button>
        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
          حذف
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  let userToDelete = null;

  function viewUser(userId) {
    // عرض تفاصيل المستخدم
    const modalBody = document.getElementById("userModalBody");
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById("userModal"));
    modal.show();

    // محاكاة تحميل البيانات
    setTimeout(() => {
      modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-4 text-center">
                    <img src="https://ui-avatars.com/api/?name=User&background=007bff&color=fff&size=120" 
                         class="rounded-circle mb-3" alt="صورة المستخدم">
                </div>
                <div class="col-md-8">
                    <h5>معلومات المستخدم</h5>
                    <p><strong>الاسم:</strong> اسم المستخدم</p>
                    <p><strong>البريد:</strong> <EMAIL></p>
                    <p><strong>الدور:</strong> محامي</p>
                    <p><strong>تاريخ التسجيل:</strong> 2025-01-18</p>
                    <p><strong>آخر دخول:</strong> منذ 5 دقائق</p>
                </div>
            </div>
        `;
    }, 1000);
  }

  function editUser(userId) {
    showAlert("info", `سيتم إضافة وظيفة تعديل المستخدم رقم ${userId} قريباً`);
  }

  function toggleUserStatus(userId, isActive) {
    const action = isActive ? "إلغاء تفعيل" : "تفعيل";
    if (confirm(`هل أنت متأكد من ${action} هذا المستخدم؟`)) {
      showAlert("success", `سيتم ${action} المستخدم (وظيفة تجريبية)`);
      // إعادة تحميل الصفحة لإظهار التغيير
      location.reload();
    }
  }

  function deleteUser(userId, userName) {
    userToDelete = userId;
    document.getElementById("userName").textContent = userName;

    const deleteModal = new bootstrap.Modal(
      document.getElementById("deleteModal")
    );
    deleteModal.show();
  }

  function viewUserPermissions(userId) {
    // إظهار modal لعرض صلاحيات المستخدم
    const modalTitle = document.querySelector("#userModal .modal-title");
    const modalBody = document.getElementById("userModalBody");

    modalTitle.textContent = "صلاحيات المستخدم";
    modalBody.innerHTML =
      '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    const modal = new bootstrap.Modal(document.getElementById("userModal"));
    modal.show();

    // جلب صلاحيات المستخدم
    fetch(`/admin/permissions/users/${userId}`)
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          const user = data.user;
          modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-4 text-center">
                        <img src="https://ui-avatars.com/api/?name=${
                          user.full_name
                        }&background=007bff&color=fff&size=120"
                             class="rounded-circle mb-3" alt="صورة المستخدم">
                        <h6>${user.full_name}</h6>
                        <p class="text-muted">${user.username}</p>
                    </div>
                    <div class="col-md-8">
                        <h6>معلومات الدور والصلاحيات</h6>
                        <p><strong>الدور الحالي:</strong>
                           <span class="badge bg-primary">${
                             user.role_name || "غير محدد"
                           }</span>
                        </p>
                        <p><strong>عدد الصلاحيات:</strong>
                           <span class="badge bg-info">${
                             user.permissions.length
                           }</span>
                        </p>

                        ${
                          user.permissions.length > 0
                            ? `
                        <h6 class="mt-3">الصلاحيات المخصصة:</h6>
                        <div class="row">
                            ${user.permissions
                              .map(
                                (perm) => `
                                <div class="col-md-6 mb-2">
                                    <span class="badge bg-success">${perm}</span>
                                </div>
                            `
                              )
                              .join("")}
                        </div>
                        `
                            : '<p class="text-muted">لا توجد صلاحيات مخصصة</p>'
                        }

                        <div class="mt-3">
                            <a href="/admin/permissions" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit me-1"></i>
                                إدارة الصلاحيات
                            </a>
                        </div>
                    </div>
                </div>
            `;
        } else {
          modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${data.message}
                </div>
            `;
        }
      })
      .catch((error) => {
        console.error("Error:", error);
        modalBody.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                حدث خطأ في جلب بيانات المستخدم
            </div>
        `;
      });
  }

  function confirmDelete() {
    if (userToDelete) {
      showAlert(
        "success",
        `سيتم حذف المستخدم رقم ${userToDelete} (وظيفة تجريبية)`
      );

      // إغلاق النافذة
      const deleteModal = bootstrap.Modal.getInstance(
        document.getElementById("deleteModal")
      );
      deleteModal.hide();

      userToDelete = null;

      // إعادة تحميل الصفحة لإظهار التغيير
      location.reload();
    }
  }
</script>
{% endblock %}
