#!/usr/bin/env python3
"""
أداة اختبار مسارات نظام الشؤون القانونية
"""

import requests
import sys
import time

def test_route(url, route_name, expected_status=200):
    """اختبار مسار واحد"""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == expected_status:
            print(f"✅ {route_name}: يعمل بشكل صحيح ({response.status_code})")
            return True
        else:
            print(f"⚠️  {route_name}: حالة غير متوقعة ({response.status_code})")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ {route_name}: لا يمكن الاتصال")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ {route_name}: انتهت مهلة الاتصال")
        return False
    except Exception as e:
        print(f"❌ {route_name}: خطأ - {e}")
        return False

def test_login_required_route(url, route_name):
    """اختبار مسار يتطلب تسجيل دخول"""
    try:
        response = requests.get(url, timeout=10, allow_redirects=False)
        if response.status_code == 302:  # إعادة توجيه لصفحة تسجيل الدخول
            print(f"✅ {route_name}: محمي بشكل صحيح (302)")
            return True
        elif response.status_code == 200:
            print(f"⚠️  {route_name}: يعمل ولكن قد لا يكون محمي")
            return True
        else:
            print(f"❌ {route_name}: حالة غير متوقعة ({response.status_code})")
            return False
    except Exception as e:
        print(f"❌ {route_name}: خطأ - {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار مسارات نظام الشؤون القانونية")
    print("="*60)
    
    base_url = "http://localhost:5000"
    
    # التحقق من أن الخادم يعمل
    print("🔍 التحقق من حالة الخادم...")
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code in [200, 302]:
            print("✅ الخادم يعمل بشكل صحيح")
        else:
            print(f"⚠️  الخادم يعمل ولكن بحالة غير متوقعة ({response.status_code})")
    except:
        print("❌ الخادم لا يعمل. تأكد من تشغيل: python app.py")
        return
    
    print("\n📋 اختبار المسارات العامة:")
    print("-" * 30)
    
    public_routes = [
        ("/", "الصفحة الرئيسية"),
        ("/login", "صفحة تسجيل الدخول"),
    ]
    
    public_success = 0
    for route, name in public_routes:
        if test_route(f"{base_url}{route}", name, 200):
            public_success += 1
    
    print(f"\n📊 المسارات العامة: {public_success}/{len(public_routes)}")
    
    print("\n🔒 اختبار المسارات المحمية:")
    print("-" * 30)
    
    protected_routes = [
        ("/dashboard", "لوحة التحكم"),
        ("/cases/", "قائمة القضايا"),
        ("/cases/add", "إضافة قضية"),
        ("/clients/", "قائمة العملاء"),
        ("/clients/add", "إضافة عميل"),
        ("/lawyers/", "قائمة المحامين"),
        ("/appointments/", "قائمة المواعيد"),
        ("/documents/", "قائمة المستندات"),
        ("/invoices/", "قائمة الفواتير"),
        ("/reports/", "التقارير"),
    ]
    
    protected_success = 0
    for route, name in protected_routes:
        if test_login_required_route(f"{base_url}{route}", name):
            protected_success += 1
    
    print(f"\n📊 المسارات المحمية: {protected_success}/{len(protected_routes)}")
    
    print("\n🧪 اختبار مسارات خاصة:")
    print("-" * 30)
    
    # اختبار مسار غير موجود
    test_route(f"{base_url}/nonexistent", "مسار غير موجود", 404)
    
    # اختبار مسار قضية محددة
    test_login_required_route(f"{base_url}/cases/1", "عرض قضية محددة")
    
    total_routes = len(public_routes) + len(protected_routes)
    total_success = public_success + protected_success
    
    print("\n" + "="*60)
    print("📊 ملخص النتائج:")
    print("="*60)
    print(f"✅ المسارات التي تعمل: {total_success}/{total_routes}")
    print(f"📈 معدل النجاح: {(total_success/total_routes)*100:.1f}%")
    
    if total_success == total_routes:
        print("\n🎉 جميع المسارات تعمل بشكل مثالي!")
        print("🚀 النظام جاهز للاستخدام")
    elif total_success >= total_routes * 0.8:
        print("\n✅ معظم المسارات تعمل بشكل جيد")
        print("⚠️  قد تحتاج لفحص بعض المسارات")
    else:
        print("\n❌ هناك مشاكل في عدة مسارات")
        print("🔧 يرجى مراجعة الأخطاء وإصلاحها")
    
    print("\n💡 نصائح:")
    print("- تأكد من تشغيل الخادم: python app.py")
    print("- للوصول للنظام: http://localhost:5000")
    print("- بيانات الدخول: admin / admin123")
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
