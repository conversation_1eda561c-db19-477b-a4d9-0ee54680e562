# نظام الشؤون القانونية

نظام شامل لإدارة مكاتب المحاماة والشؤون القانونية مطور بلغة Python باستخدام إطار عمل Flask.

## 🌟 المميزات الرئيسية

### 📁 إدارة القضايا
- تسجيل وتتبع القضايا القانونية
- إدارة مراحل التقاضي والجلسات
- ربط القضايا بالعملاء والمحامين
- تتبع الجدول الزمني للأحداث

### 👥 إدارة العملاء والمحامين
- قاعدة بيانات شاملة للعملاء
- ملفات تعريف المحامين والتخصصات
- تتبع تاريخ التعامل

### 📅 نظام المواعيد والجلسات
- جدولة المواعيد والاستشارات
- تذكيرات الجلسات
- تقويم تفاعلي للمواعيد

### 📄 إدارة المستندات
- رفع وتخزين المستندات القانونية
- تصنيف وفهرسة الملفات
- ربط المستندات بالقضايا

### 💰 النظام المالي
- إنشاء وإدارة الفواتير
- تتبع المدفوعات والمستحقات
- تقارير مالية شاملة

### 📊 التقارير والإحصائيات
- تقارير أداء المحامين
- إحصائيات القضايا
- تقارير مالية وإدارية

## 🛠️ التقنيات المستخدمة

- **Backend**: Python Flask
- **Database**: SQLite (قابل للترقية إلى PostgreSQL)
- **ORM**: SQLAlchemy
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Authentication**: Flask-Login
- **File Upload**: Flask-Uploads
- **Charts**: Chart.js

## 📋 متطلبات النظام

- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- متصفح ويب حديث

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd AhmedSaeid
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python app.py
```

### 4. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## 🔐 بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📁 هيكل المشروع

```
AhmedSaeid/
├── app.py                 # الملف الرئيسي للتطبيق
├── models.py              # نماذج قاعدة البيانات
├── requirements.txt       # متطلبات Python
├── .env                   # متغيرات البيئة
├── routes/               # مسارات التطبيق
│   ├── cases.py          # مسارات القضايا
│   ├── clients.py        # مسارات العملاء
│   ├── lawyers.py        # مسارات المحامين
│   ├── appointments.py   # مسارات المواعيد
│   ├── documents.py      # مسارات المستندات
│   ├── invoices.py       # مسارات الفواتير
│   └── reports.py        # مسارات التقارير
├── templates/            # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── index.html        # الصفحة الرئيسية
│   ├── login.html        # صفحة تسجيل الدخول
│   ├── dashboard.html    # لوحة التحكم
│   └── [modules]/        # قوالب الوحدات
└── uploads/              # مجلد رفع الملفات
```

## 🗄️ قاعدة البيانات

النظام يستخدم قاعدة بيانات SQLite بالجداول التالية:

- **users**: المستخدمون والصلاحيات
- **clients**: بيانات العملاء
- **lawyers**: بيانات المحامين
- **cases**: القضايا القانونية
- **case_sessions**: جلسات القضايا
- **appointments**: المواعيد
- **documents**: المستندات
- **invoices**: الفواتير
- **payments**: المدفوعات
- **case_notes**: ملاحظات القضايا
- **case_timeline**: الجدول الزمني للأحداث

## 🔧 الإعدادات

يمكن تخصيص الإعدادات من خلال ملف `.env`:

```env
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite:///legal_system.db
FLASK_ENV=development
FLASK_DEBUG=True
```

## 📱 الواجهات

النظام يدعم:
- واجهة سطح المكتب (Desktop)
- واجهة الأجهزة اللوحية (Tablet)
- واجهة الهواتف المحمولة (Mobile)

## 🛡️ الأمان

- تشفير كلمات المرور
- جلسات آمنة
- حماية من CSRF
- تحكم في الصلاحيات

## 🚧 الحالة الحالية

✅ **مكتمل**:
- تصميم قاعدة البيانات
- النماذج الأساسية
- واجهة المستخدم الأساسية
- نظام تسجيل الدخول
- لوحة التحكم

🔄 **قيد التطوير**:
- وظائف إدارة القضايا
- وظائف إدارة العملاء
- نظام المواعيد
- إدارة المستندات
- النظام المالي
- التقارير

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للدعم والاستفسارات، يرجى التواصل عبر:
- البريد الإلكتروني: [your-email]
- GitHub Issues: [repository-issues-url]

---

© 2025 نظام الشؤون القانونية. جميع الحقوق محفوظة.
