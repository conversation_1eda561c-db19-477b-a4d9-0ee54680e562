#!/usr/bin/env python3
"""
أداة تشخيص نظام الشؤون القانونية
"""

import sys
import os
import platform
import subprocess
import importlib.util

def print_system_info():
    """طباعة معلومات النظام"""
    print("="*60)
    print("🖥️  معلومات النظام")
    print("="*60)
    print(f"نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"معمارية النظام: {platform.machine()}")
    print(f"Python: {sys.version}")
    print(f"مسار Python: {sys.executable}")
    print(f"المجلد الحالي: {os.getcwd()}")
    print()

def check_python_packages():
    """فحص حزم Python"""
    print("📦 فحص حزم Python")
    print("-" * 30)
    
    required_packages = [
        ('flask', 'Flask'),
        ('flask_sqlalchemy', 'Flask-SQLAlchemy'),
        ('flask_login', 'Flask-Login'),
        ('werkzeug', 'Werkzeug'),
        ('dotenv', 'python-dotenv'),
        ('bcrypt', 'bcrypt'),
        ('email_validator', 'email-validator')
    ]
    
    installed_count = 0
    
    for import_name, package_name in required_packages:
        try:
            spec = importlib.util.find_spec(import_name)
            if spec is not None:
                module = importlib.import_module(import_name)
                version = getattr(module, '__version__', 'غير معروف')
                print(f"✅ {package_name}: {version}")
                installed_count += 1
            else:
                print(f"❌ {package_name}: غير مثبت")
        except Exception as e:
            print(f"❌ {package_name}: خطأ - {e}")
    
    print(f"\n📊 مثبت: {installed_count}/{len(required_packages)}")
    print()

def check_project_files():
    """فحص ملفات المشروع"""
    print("📁 فحص ملفات المشروع")
    print("-" * 30)
    
    required_files = [
        'app.py',
        'models.py',
        'requirements.txt',
        '.env'
    ]
    
    optional_files = [
        'run.py',
        'fix_flask.py',
        'network_config.py',
        'start_server.bat'
    ]
    
    required_dirs = [
        'routes',
        'templates',
        'uploads'
    ]
    
    print("الملفات المطلوبة:")
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size} بايت)")
        else:
            print(f"❌ {file} مفقود")
    
    print("\nالملفات الاختيارية:")
    for file in optional_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size} بايت)")
        else:
            print(f"⚠️  {file} مفقود")
    
    print("\nالمجلدات:")
    for dir_name in required_dirs:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            files_count = len(os.listdir(dir_name))
            print(f"✅ {dir_name}/ ({files_count} ملف)")
        else:
            print(f"❌ {dir_name}/ مفقود")
    
    print()

def check_database():
    """فحص قاعدة البيانات"""
    print("🗄️  فحص قاعدة البيانات")
    print("-" * 30)
    
    db_file = 'legal_system.db'
    
    if os.path.exists(db_file):
        size = os.path.getsize(db_file)
        print(f"✅ قاعدة البيانات موجودة ({size} بايت)")
        
        # محاولة الاتصال بقاعدة البيانات
        try:
            import sqlite3
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            conn.close()
            
            print(f"📊 عدد الجداول: {len(tables)}")
            if tables:
                print("الجداول الموجودة:")
                for table in tables:
                    print(f"   - {table[0]}")
        except Exception as e:
            print(f"⚠️  خطأ في قراءة قاعدة البيانات: {e}")
    else:
        print("❌ قاعدة البيانات غير موجودة")
        print("💡 ستُنشأ تلقائياً عند تشغيل التطبيق")
    
    print()

def test_flask_app():
    """اختبار تطبيق Flask"""
    print("🧪 اختبار تطبيق Flask")
    print("-" * 30)
    
    try:
        # محاولة استيراد التطبيق
        import app
        print("✅ تم استيراد التطبيق بنجاح")
        
        # التحقق من Flask app
        if hasattr(app, 'app'):
            print("✅ كائن Flask موجود")
            
            # التحقق من قاعدة البيانات
            if hasattr(app, 'db'):
                print("✅ قاعدة البيانات مهيأة")
            else:
                print("❌ قاعدة البيانات غير مهيأة")
            
            # التحقق من النماذج
            if hasattr(app, 'User'):
                print("✅ النماذج محملة")
            else:
                print("❌ النماذج غير محملة")
        else:
            print("❌ كائن Flask غير موجود")
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
    except Exception as e:
        print(f"⚠️  تحذير: {e}")
    
    print()

def check_network():
    """فحص إعدادات الشبكة"""
    print("🌐 فحص إعدادات الشبكة")
    print("-" * 30)
    
    try:
        import socket
        
        # فحص المنفذ 5000
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 5000))
        sock.close()
        
        if result == 0:
            print("⚠️  المنفذ 5000 مستخدم")
        else:
            print("✅ المنفذ 5000 متاح")
        
        # الحصول على IP المحلي
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"🖥️  IP المحلي: {local_ip}")
        
    except Exception as e:
        print(f"❌ خطأ في فحص الشبكة: {e}")
    
    print()

def provide_solutions():
    """تقديم الحلول"""
    print("💡 الحلول المقترحة")
    print("-" * 30)
    
    print("إذا كان هناك مشاكل، جرب:")
    print("1. تشغيل أداة الإصلاح:")
    print("   python fix_flask.py")
    print()
    print("2. إعادة تثبيت المتطلبات:")
    print("   pip install -r requirements.txt --upgrade")
    print()
    print("3. استخدام الإصلاح السريع (Windows):")
    print("   quick_fix.bat")
    print()
    print("4. إنشاء بيئة افتراضية جديدة:")
    print("   python -m venv venv")
    print("   venv\\Scripts\\activate  # Windows")
    print("   source venv/bin/activate  # Linux/Mac")
    print("   pip install -r requirements.txt")
    print()
    print("5. تشغيل التطبيق:")
    print("   python app.py")
    print()

def main():
    """الدالة الرئيسية"""
    print("🔍 أداة تشخيص نظام الشؤون القانونية")
    print()
    
    print_system_info()
    check_python_packages()
    check_project_files()
    check_database()
    test_flask_app()
    check_network()
    provide_solutions()
    
    print("="*60)
    print("✅ انتهى التشخيص")
    print("="*60)

if __name__ == "__main__":
    main()
