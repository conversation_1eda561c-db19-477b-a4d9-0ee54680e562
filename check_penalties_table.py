#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جدول الجزاءات في قواعد البيانات المختلفة
"""

import sqlite3
import os

def check_database(db_path):
    """فحص قاعدة بيانات محددة"""
    print(f"\n🔍 فحص قاعدة البيانات: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ الملف غير موجود: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول الجزاءات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='penalties'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ جدول الجزاءات غير موجود")
            return
        
        print("✅ جدول الجزاءات موجود")
        
        # فحص أعمدة الجدول
        cursor.execute("PRAGMA table_info(penalties)")
        columns = cursor.fetchall()
        
        print("📋 أعمدة الجدول:")
        for column in columns:
            print(f"  - {column[1]} ({column[2]})")
        
        # التحقق من وجود عمود category
        column_names = [column[1] for column in columns]
        if 'category' in column_names:
            print("✅ عمود category موجود")
        else:
            print("❌ عمود category مفقود")
        
        # عدد السجلات
        cursor.execute("SELECT COUNT(*) FROM penalties")
        count = cursor.fetchone()[0]
        print(f"📊 عدد السجلات: {count}")
        
        # عرض عينة من البيانات إذا كانت موجودة
        if count > 0:
            cursor.execute("SELECT * FROM penalties LIMIT 3")
            rows = cursor.fetchall()
            print("📄 عينة من البيانات:")
            for i, row in enumerate(rows, 1):
                print(f"  السجل {i}: {row[:5]}...")  # أول 5 أعمدة فقط
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص جداول الجزاءات في قواعد البيانات المختلفة")
    print("=" * 60)
    
    # قواعد البيانات المحتملة
    databases = [
        'legal_system.db',
        'instance/legal_system.db'
    ]
    
    for db_path in databases:
        check_database(db_path)
    
    print("\n" + "=" * 60)
    print("انتهى الفحص")

if __name__ == "__main__":
    main()
