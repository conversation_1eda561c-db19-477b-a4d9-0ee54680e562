from django.shortcuts import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, send_file
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from decimal import Decimal
import uuid
import os
from werkzeug.utils import secure_filename
from permissions_manager import permission_required

bp = Blueprint('warnings', __name__, url_prefix='/warnings')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from django.shortcuts import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        return {}

def get_db():
    """الحصول على كائن قاعدة البيانات"""
    try:
        from django.shortcuts import current_app
        return current_app.extensions['sqlalchemy'].db
    except (RuntimeError, KeyError):
        return None

def allowed_file(filename):
    """التحقق من أن نوع الملف مسموح"""
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    """التأكد من وجود مجلد رفع الملفات"""
    upload_folder = os.path.join(current_app.root_path, 'static', 'uploads', 'warnings')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    return upload_folder

@bp.route('/')
@login_required
@permission_required('view_warnings')
def index():
    """عرض الإنذارات"""
    try:
        models = get_models()
        Warning = models.get('Warning')
        Employee = models.get('Employee')
        Department = models.get('Department')
        
        if not all([Warning, Employee, Department]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # فلترة البيانات
        search = request.GET.get('search', '').strip()
        employee_id = request.GET.get('employee_id', type=int)
        department_id = request.GET.get('department_id', type=int)
        warning_type = request.GET.get('warning_type', '').strip()
        category = request.GET.get('category', '').strip()
        severity = request.GET.get('severity', '').strip()
        status = request.GET.get('status', '').strip()
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        
        # بناء الاستعلام
        query = Warning.query.join(Employee)
        
        if search:
            from sqlalchemy import or_
            query = query.filter(
                or_(
                    Employee.full_name.contains(search),
                    Warning.title.contains(search),
                    Warning.description.contains(search)
                )
            )
        
        if employee_id:
            query = query.filter(Warning.employee_id == employee_id)
        
        if department_id:
            query = query.filter(Employee.department_id == department_id)
        
        if warning_type:
            query = query.filter(Warning.warning_type == warning_type)
        
        if category:
            query = query.filter(Warning.category == category)
        
        if severity:
            query = query.filter(Warning.severity == severity)
        
        if status:
            query = query.filter(Warning.status == status)
        
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(Warning.incident_date >= date_from_obj)
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                query = query.filter(Warning.incident_date <= date_to_obj)
            except ValueError:
                pass
        
        # ترتيب النتائج
        warnings = query.order_by(Warning.created_at.desc()).all()
        
        # جلب قوائم الفلترة
        employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
        departments = Department.query.order_by(Department.name).all()
        
        # أنواع الإنذارات
        warning_types = [
            ('verbal', 'إنذار شفهي'),
            ('written', 'إنذار كتابي'),
            ('final', 'إنذار نهائي')
        ]
        
        # فئات الإنذارات
        categories = [
            ('attendance', 'الحضور والغياب'),
            ('performance', 'الأداء الوظيفي'),
            ('conduct', 'السلوك المهني'),
            ('safety', 'السلامة والأمان'),
            ('policy', 'مخالفة السياسات'),
            ('other', 'أخرى')
        ]
        
        # مستويات الخطورة
        severities = [
            ('low', 'منخفض'),
            ('medium', 'متوسط'),
            ('high', 'عالي'),
            ('critical', 'حرج')
        ]
        
        # حالات الإنذار
        statuses = [
            ('active', 'نشط'),
            ('resolved', 'تم الحل'),
            ('expired', 'منتهي الصلاحية')
        ]
        
        # إحصائيات سريعة
        stats = {
            'total_warnings': len(warnings),
            'active_warnings': len([w for w in warnings if w.status == 'active']),
            'resolved_warnings': len([w for w in warnings if w.status == 'resolved']),
            'critical_warnings': len([w for w in warnings if w.severity == 'critical']),
            'this_month': len([w for w in warnings if w.created_at.month == date.today().month])
        }
        
        return render('warnings/index.html',
                             warnings=warnings,
                             employees=employees,
                             departments=departments,
                             warning_types=warning_types,
                             categories=categories,
                             severities=severities,
                             statuses=statuses,
                             search=search,
                             employee_id=employee_id,
                             department_id=department_id,
                             warning_type=warning_type,
                             category=category,
                             severity=severity,
                             status=status,
                             date_from=date_from,
                             date_to=date_to,
                             stats=stats)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('view_warnings')
def add():
    """إضافة إنذار جديد"""
    if request.method == 'GET':
        try:
            models = get_models()
            Employee = models.get('Employee')
            
            if not Employee:
                flash('خطأ في تحميل النماذج', 'error')
                return redirect(url_for('warnings.index'))

            employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
            
            # أنواع الإنذارات
            warning_types = [
                ('verbal', 'إنذار شفهي'),
                ('written', 'إنذار كتابي'),
                ('final', 'إنذار نهائي')
            ]
            
            # فئات الإنذارات
            categories = [
                ('attendance', 'الحضور والغياب'),
                ('performance', 'الأداء الوظيفي'),
                ('conduct', 'السلوك المهني'),
                ('safety', 'السلامة والأمان'),
                ('policy', 'مخالفة السياسات'),
                ('other', 'أخرى')
            ]
            
            # مستويات الخطورة
            severities = [
                ('low', 'منخفض'),
                ('medium', 'متوسط'),
                ('high', 'عالي'),
                ('critical', 'حرج')
            ]
            
            return render('warnings/add.html',
                                 employees=employees,
                                 warning_types=warning_types,
                                 categories=categories,
                                 severities=severities,
                                 today=date.today())
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
            return redirect(url_for('warnings.index'))
    
    # POST request
    try:
        models = get_models()
        Warning = models.get('Warning')
        Employee = models.get('Employee')
        db = get_db()
        
        if not all([Warning, Employee, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        # جلب البيانات من النموذج
        employee_id = request.POST.get('employee_id', type=int)
        warning_type = request.POST.get('warning_type', '').strip()
        category = request.POST.get('category', '').strip()
        title = request.POST.get('title', '').strip()
        description = request.POST.get('description', '').strip()
        incident_date_str = request.POST.get('incident_date')
        severity = request.POST.get('severity', 'medium').strip()
        action_required = request.POST.get('action_required', '').strip()
        deadline_str = request.POST.get('deadline', '').strip()
        follow_up_date_str = request.POST.get('follow_up_date', '').strip()

        # التحقق من صحة البيانات
        if not all([employee_id, warning_type, category, title, description, incident_date_str]):
            return JsonResponse({'success': False, 'message': 'جميع الحقول الأساسية مطلوبة'})

        # التحقق من وجود الموظف
        employee = Employee.query.get(employee_id)
        if not employee:
            return JsonResponse({'success': False, 'message': 'الموظف غير موجود'})

        # تحويل التواريخ
        try:
            incident_date_obj = datetime.strptime(incident_date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({'success': False, 'message': 'تنسيق تاريخ الحادثة غير صحيح'})

        deadline_obj = None
        if deadline_str:
            try:
                deadline_obj = datetime.strptime(deadline_str, '%Y-%m-%d').date()
            except ValueError:
                return JsonResponse({'success': False, 'message': 'تنسيق تاريخ الموعد النهائي غير صحيح'})

        follow_up_date_obj = None
        if follow_up_date_str:
            try:
                follow_up_date_obj = datetime.strptime(follow_up_date_str, '%Y-%m-%d').date()
            except ValueError:
                return JsonResponse({'success': False, 'message': 'تنسيق تاريخ المتابعة غير صحيح'})

        # معالجة رفع الملف
        attachment_path = None
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                # إنشاء مجلد الرفع
                upload_folder = ensure_upload_folder()
                
                # إنشاء اسم ملف فريد
                original_filename = secure_filename(file.filename)
                file_extension = original_filename.rsplit('.', 1)[1].lower()
                unique_filename = f"warning_{employee_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
                file_path = os.path.join(upload_folder, unique_filename)
                
                # حفظ الملف
                file.save(file_path)
                attachment_path = f"uploads/warnings/{unique_filename}"

        # إنشاء الإنذار
        warning = Warning(
            employee_id=employee_id,
            warning_type=warning_type,
            category=category,
            title=title,
            description=description,
            incident_date=incident_date_obj,
            severity=severity,
            action_required=action_required,
            deadline=deadline_obj,
            follow_up_date=follow_up_date_obj,
            attachment_path=attachment_path,
            status='active',
            created_by=current_user.id
        )

        db.add(warning)
        db.save()

        return JsonResponse({'success': True, 'message': 'تم إضافة الإنذار بنجاح'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:warning_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('manage_warnings')
def edit(warning_id):
    """تعديل إنذار"""
    try:
        models = get_models()
        Warning = models.get('Warning')
        Employee = models.get('Employee')
        db = get_db()
        
        if not all([Warning, Employee, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('warnings.index'))

        warning = Warning.query.get_or_404(warning_id)
        
        if request.method == 'GET':
            employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
            
            # أنواع الإنذارات
            warning_types = [
                ('verbal', 'إنذار شفهي'),
                ('written', 'إنذار كتابي'),
                ('final', 'إنذار نهائي')
            ]
            
            # فئات الإنذارات
            categories = [
                ('attendance', 'الحضور والغياب'),
                ('performance', 'الأداء الوظيفي'),
                ('conduct', 'السلوك المهني'),
                ('safety', 'السلامة والأمان'),
                ('policy', 'مخالفة السياسات'),
                ('other', 'أخرى')
            ]
            
            # مستويات الخطورة
            severities = [
                ('low', 'منخفض'),
                ('medium', 'متوسط'),
                ('high', 'عالي'),
                ('critical', 'حرج')
            ]
            
            # حالات الإنذار
            statuses = [
                ('active', 'نشط'),
                ('resolved', 'تم الحل'),
                ('expired', 'منتهي الصلاحية')
            ]
            
            return render('warnings/edit.html',
                                 warning=warning,
                                 employees=employees,
                                 warning_types=warning_types,
                                 categories=categories,
                                 severities=severities,
                                 statuses=statuses)
        
        # POST request - تحديث الإنذار
        # جلب البيانات من النموذج
        employee_id = request.POST.get('employee_id', type=int)
        warning_type = request.POST.get('warning_type', '').strip()
        category = request.POST.get('category', '').strip()
        title = request.POST.get('title', '').strip()
        description = request.POST.get('description', '').strip()
        incident_date_str = request.POST.get('incident_date')
        severity = request.POST.get('severity', 'medium').strip()
        status = request.POST.get('status', 'active').strip()
        action_required = request.POST.get('action_required', '').strip()
        deadline_str = request.POST.get('deadline', '').strip()
        follow_up_date_str = request.POST.get('follow_up_date', '').strip()
        resolution_notes = request.POST.get('resolution_notes', '').strip()

        # التحقق من صحة البيانات
        if not all([employee_id, warning_type, category, title, description, incident_date_str]):
            return JsonResponse({'success': False, 'message': 'جميع الحقول الأساسية مطلوبة'})

        # التحقق من وجود الموظف
        employee = Employee.query.get(employee_id)
        if not employee:
            return JsonResponse({'success': False, 'message': 'الموظف غير موجود'})

        # تحويل التواريخ
        try:
            incident_date_obj = datetime.strptime(incident_date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({'success': False, 'message': 'تنسيق تاريخ الحادثة غير صحيح'})

        deadline_obj = None
        if deadline_str:
            try:
                deadline_obj = datetime.strptime(deadline_str, '%Y-%m-%d').date()
            except ValueError:
                return JsonResponse({'success': False, 'message': 'تنسيق تاريخ الموعد النهائي غير صحيح'})

        follow_up_date_obj = None
        if follow_up_date_str:
            try:
                follow_up_date_obj = datetime.strptime(follow_up_date_str, '%Y-%m-%d').date()
            except ValueError:
                return JsonResponse({'success': False, 'message': 'تنسيق تاريخ المتابعة غير صحيح'})

        # معالجة رفع الملف الجديد
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                # حذف الملف القديم
                if warning.attachment_path:
                    try:
                        old_file_path = os.path.join(current_app.root_path, 'static', warning.attachment_path)
                        if os.path.exists(old_file_path):
                            os.remove(old_file_path)
                    except:
                        pass
                
                # إنشاء مجلد الرفع
                upload_folder = ensure_upload_folder()
                
                # إنشاء اسم ملف فريد
                original_filename = secure_filename(file.filename)
                file_extension = original_filename.rsplit('.', 1)[1].lower()
                unique_filename = f"warning_{employee_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
                file_path = os.path.join(upload_folder, unique_filename)
                
                # حفظ الملف
                file.save(file_path)
                warning.attachment_path = f"uploads/warnings/{unique_filename}"

        # تحديث البيانات
        warning.employee_id = employee_id
        warning.warning_type = warning_type
        warning.category = category
        warning.title = title
        warning.description = description
        warning.incident_date = incident_date_obj
        warning.severity = severity
        warning.status = status
        warning.action_required = action_required
        warning.deadline = deadline_obj
        warning.follow_up_date = follow_up_date_obj
        warning.resolution_notes = resolution_notes

        db.save()

        return JsonResponse({'success': True, 'message': 'تم تحديث الإنذار بنجاح'})

    except Exception as e:
        if request.method == 'POST':
            return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})
        else:
            flash(f'حدث خطأ: {str(e)}', 'error')
            return redirect(url_for('warnings.index'))

@bp.route('/<int:warning_id>/delete', methods=['POST'])
@login_required
@permission_required('manage_warnings')
def delete(warning_id):
    """حذف إنذار"""
    try:
        models = get_models()
        Warning = models.get('Warning')
        db = get_db()

        if not all([Warning, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        warning = Warning.query.get_or_404(warning_id)

        # حذف الملف المرفق إن وجد
        if warning.attachment_path:
            try:
                file_path = os.path.join(current_app.root_path, 'static', warning.attachment_path)
                if os.path.exists(file_path):
                    os.remove(file_path)
            except:
                pass  # تجاهل أخطاء حذف الملف

        db.delete(warning)
        db.save()

        return JsonResponse({'success': True, 'message': 'تم حذف الإنذار بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/report')
@login_required
@permission_required('view_warnings')
def report():
    """تقرير الإنذارات"""
    try:
        models = get_models()
        Warning = models.get('Warning')
        Employee = models.get('Employee')
        Department = models.get('Department')

        if not all([Warning, Employee, Department]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # فلترة البيانات
        employee_id = request.GET.get('employee_id', type=int)
        department_id = request.GET.get('department_id', type=int)
        warning_type = request.GET.get('warning_type', '').strip()
        category = request.GET.get('category', '').strip()
        severity = request.GET.get('severity', '').strip()
        status = request.GET.get('status', '').strip()
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')

        # تواريخ افتراضية (السنة الحالية)
        if not date_from:
            date_from = date.today().replace(month=1, day=1).strftime('%Y-%m-%d')
        if not date_to:
            date_to = date.today().strftime('%Y-%m-%d')

        # بناء الاستعلام
        query = Warning.query.join(Employee)

        if employee_id:
            query = query.filter(Warning.employee_id == employee_id)

        if department_id:
            query = query.filter(Employee.department_id == department_id)

        if warning_type:
            query = query.filter(Warning.warning_type == warning_type)

        if category:
            query = query.filter(Warning.category == category)

        if severity:
            query = query.filter(Warning.severity == severity)

        if status:
            query = query.filter(Warning.status == status)

        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(Warning.incident_date >= date_from_obj, Warning.incident_date <= date_to_obj)
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return redirect(url_for('warnings.index'))

        warnings = query.order_by(Warning.incident_date.desc()).all()

        # جلب قوائم الفلترة
        employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
        departments = Department.query.order_by(Department.name).all()

        # أنواع الإنذارات
        warning_types = [
            ('verbal', 'إنذار شفهي'),
            ('written', 'إنذار كتابي'),
            ('final', 'إنذار نهائي')
        ]

        # فئات الإنذارات
        categories = [
            ('attendance', 'الحضور والغياب'),
            ('performance', 'الأداء الوظيفي'),
            ('conduct', 'السلوك المهني'),
            ('safety', 'السلامة والأمان'),
            ('policy', 'مخالفة السياسات'),
            ('other', 'أخرى')
        ]

        # مستويات الخطورة
        severities = [
            ('low', 'منخفض'),
            ('medium', 'متوسط'),
            ('high', 'عالي'),
            ('critical', 'حرج')
        ]

        # حساب الإحصائيات
        total_warnings = len(warnings)

        # إحصائيات حسب النوع
        type_stats = {}
        for warning_type_code, warning_type_name in warning_types:
            type_warnings = [w for w in warnings if w.warning_type == warning_type_code]
            type_stats[warning_type_code] = {
                'name': warning_type_name,
                'count': len(type_warnings)
            }

        # إحصائيات حسب الفئة
        category_stats = {}
        for category_code, category_name in categories:
            category_warnings = [w for w in warnings if w.category == category_code]
            category_stats[category_code] = {
                'name': category_name,
                'count': len(category_warnings)
            }

        # إحصائيات حسب الخطورة
        severity_stats = {}
        for severity_code, severity_name in severities:
            severity_warnings = [w for w in warnings if w.severity == severity_code]
            severity_stats[severity_code] = {
                'name': severity_name,
                'count': len(severity_warnings)
            }

        stats = {
            'total_warnings': total_warnings,
            'active_warnings': len([w for w in warnings if w.status == 'active']),
            'resolved_warnings': len([w for w in warnings if w.status == 'resolved']),
            'expired_warnings': len([w for w in warnings if w.status == 'expired']),
            'type_stats': type_stats,
            'category_stats': category_stats,
            'severity_stats': severity_stats
        }

        return render('warnings/report.html',
                             warnings=warnings,
                             employees=employees,
                             departments=departments,
                             warning_types=warning_types,
                             categories=categories,
                             severities=severities,
                             employee_id=employee_id,
                             department_id=department_id,
                             warning_type=warning_type,
                             category=category,
                             severity=severity,
                             status=status,
                             date_from=date_from,
                             date_to=date_to,
                             stats=stats,
                             report_title="تقرير الإنذارات",
                             report_period=f"من {date_from} إلى {date_to}",
                             report_date=date.today().strftime('%Y-%m-%d'))
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('warnings.index'))
