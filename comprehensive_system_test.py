#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام
Comprehensive System Test
"""

import os
import sys
import sqlite3
import requests
import time
from datetime import datetime

def test_database_connectivity():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔍 اختبار الاتصال بقاعدة البيانات...")
    
    databases = ['legal_system.db', 'instance/legal_system.db']
    results = {}
    
    for db_path in databases:
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # اختبار استعلام بسيط
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                # اختبار جداول مهمة
                required_tables = ['users', 'employees', 'departments', 'attendance', 'penalties', 'warnings', 'leave_requests']
                existing_tables = [table[0] for table in tables]
                missing_tables = [table for table in required_tables if table not in existing_tables]
                
                results[db_path] = {
                    'status': 'success',
                    'tables_count': len(tables),
                    'missing_tables': missing_tables
                }
                
                conn.close()
                print(f"  ✅ {db_path}: {len(tables)} جدول")
                if missing_tables:
                    print(f"    ⚠️ جداول مفقودة: {', '.join(missing_tables)}")
                
            except Exception as e:
                results[db_path] = {'status': 'error', 'error': str(e)}
                print(f"  ❌ {db_path}: {e}")
        else:
            results[db_path] = {'status': 'not_found'}
            print(f"  ❌ {db_path}: غير موجود")
    
    return results

def test_python_files():
    """اختبار ملفات Python"""
    print("\n🔍 اختبار ملفات Python...")
    
    python_files = []
    
    # الملفات الرئيسية
    main_files = ['app.py', 'models.py', 'update_database.py']
    for file in main_files:
        if os.path.exists(file):
            python_files.append(file)
    
    # ملفات المسارات
    if os.path.exists('routes'):
        for file in os.listdir('routes'):
            if file.endswith('.py') and file != '__init__.py':
                python_files.append(os.path.join('routes', file))
    
    results = {}
    
    for file_path in python_files:
        try:
            # فحص بناء الجملة
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            compile(content, file_path, 'exec')
            results[file_path] = {'status': 'success'}
            print(f"  ✅ {file_path}")
            
        except SyntaxError as e:
            results[file_path] = {'status': 'syntax_error', 'error': str(e)}
            print(f"  ❌ {file_path}: خطأ في بناء الجملة - {e}")
        except Exception as e:
            results[file_path] = {'status': 'error', 'error': str(e)}
            print(f"  ⚠️ {file_path}: {e}")
    
    return results

def test_templates():
    """اختبار القوالب"""
    print("\n🔍 اختبار القوالب...")
    
    template_dirs = ['templates']
    template_files = []
    
    for template_dir in template_dirs:
        if os.path.exists(template_dir):
            for root, dirs, files in os.walk(template_dir):
                for file in files:
                    if file.endswith('.html'):
                        template_files.append(os.path.join(root, file))
    
    results = {}
    
    for template_path in template_files:
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص أساسي للقوالب
            issues = []
            
            # فحص العلامات المفتوحة/المغلقة
            if content.count('{{') != content.count('}}'):
                issues.append('عدم تطابق علامات Jinja2')
            
            if content.count('{%') != content.count('%}'):
                issues.append('عدم تطابق علامات Jinja2 للتحكم')
            
            # فحص العلامات HTML الأساسية
            if '<html>' in content and '</html>' not in content:
                issues.append('علامة HTML غير مغلقة')
            
            if issues:
                results[template_path] = {'status': 'warning', 'issues': issues}
                print(f"  ⚠️ {template_path}: {', '.join(issues)}")
            else:
                results[template_path] = {'status': 'success'}
                print(f"  ✅ {template_path}")
                
        except Exception as e:
            results[template_path] = {'status': 'error', 'error': str(e)}
            print(f"  ❌ {template_path}: {e}")
    
    return results

def test_static_files():
    """اختبار الملفات الثابتة"""
    print("\n🔍 اختبار الملفات الثابتة...")
    
    static_dirs = ['static']
    important_files = [
        'static/css/style.css',
        'static/js/main.js',
        'static/images/logo.svg'
    ]
    
    results = {}
    
    for file_path in important_files:
        if os.path.exists(file_path):
            try:
                file_size = os.path.getsize(file_path)
                results[file_path] = {'status': 'success', 'size': file_size}
                print(f"  ✅ {file_path} ({file_size} بايت)")
            except Exception as e:
                results[file_path] = {'status': 'error', 'error': str(e)}
                print(f"  ❌ {file_path}: {e}")
        else:
            results[file_path] = {'status': 'not_found'}
            print(f"  ⚠️ {file_path}: غير موجود")
    
    return results

def test_configuration():
    """اختبار الإعدادات"""
    print("\n🔍 اختبار الإعدادات...")
    
    config_files = ['.env', 'requirements.txt']
    results = {}
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if config_file == '.env':
                    # فحص المتغيرات المهمة
                    required_vars = ['SECRET_KEY', 'DATABASE_URL']
                    missing_vars = []
                    
                    for var in required_vars:
                        if var not in content:
                            missing_vars.append(var)
                    
                    if missing_vars:
                        results[config_file] = {'status': 'warning', 'missing_vars': missing_vars}
                        print(f"  ⚠️ {config_file}: متغيرات مفقودة - {', '.join(missing_vars)}")
                    else:
                        results[config_file] = {'status': 'success'}
                        print(f"  ✅ {config_file}")
                
                elif config_file == 'requirements.txt':
                    lines = content.strip().split('\n')
                    results[config_file] = {'status': 'success', 'packages_count': len(lines)}
                    print(f"  ✅ {config_file} ({len(lines)} حزمة)")
                
            except Exception as e:
                results[config_file] = {'status': 'error', 'error': str(e)}
                print(f"  ❌ {config_file}: {e}")
        else:
            results[config_file] = {'status': 'not_found'}
            print(f"  ⚠️ {config_file}: غير موجود")
    
    return results

def generate_test_report(all_results):
    """إنشاء تقرير الاختبار"""
    print("\n" + "="*60)
    print("📋 تقرير الاختبار الشامل")
    print("="*60)
    
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    warnings = 0
    
    for category, results in all_results.items():
        print(f"\n📂 {category}:")
        
        for item, result in results.items():
            total_tests += 1
            status = result.get('status', 'unknown')
            
            if status == 'success':
                passed_tests += 1
                print(f"  ✅ {item}")
            elif status == 'error' or status == 'syntax_error':
                failed_tests += 1
                error = result.get('error', 'خطأ غير معروف')
                print(f"  ❌ {item}: {error}")
            elif status == 'warning':
                warnings += 1
                issues = result.get('issues', result.get('missing_vars', ['مشكلة غير محددة']))
                print(f"  ⚠️ {item}: {', '.join(issues)}")
            elif status == 'not_found':
                warnings += 1
                print(f"  ⚠️ {item}: غير موجود")
    
    print(f"\n📊 الإحصائيات:")
    print(f"  - إجمالي الاختبارات: {total_tests}")
    print(f"  - نجح: {passed_tests}")
    print(f"  - فشل: {failed_tests}")
    print(f"  - تحذيرات: {warnings}")
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    print(f"  - معدل النجاح: {success_rate:.1f}%")
    
    if failed_tests == 0:
        print("\n🎉 جميع الاختبارات الأساسية نجحت!")
        return True
    else:
        print(f"\n⚠️ يوجد {failed_tests} اختبار فاشل يحتاج إلى إصلاح")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار الشامل للنظام...")
    print("="*60)
    
    # تشغيل جميع الاختبارات
    all_results = {
        'قواعد البيانات': test_database_connectivity(),
        'ملفات Python': test_python_files(),
        'القوالب': test_templates(),
        'الملفات الثابتة': test_static_files(),
        'الإعدادات': test_configuration()
    }
    
    # إنشاء التقرير
    success = generate_test_report(all_results)
    
    # حفظ التقرير
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'test_report_{timestamp}.txt'
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"تقرير الاختبار الشامل - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*60 + "\n\n")
            
            for category, results in all_results.items():
                f.write(f"{category}:\n")
                for item, result in results.items():
                    status = result.get('status', 'unknown')
                    f.write(f"  {status}: {item}\n")
                f.write("\n")
        
        print(f"\n💾 تم حفظ التقرير في: {report_file}")
    except Exception as e:
        print(f"\n⚠️ لم يتم حفظ التقرير: {e}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
