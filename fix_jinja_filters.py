#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إصلاح Django template filters إلى Jinja2 filters
"""

import os
import re
import shutil
from datetime import datetime

def fix_jinja_filters():
    """إصلاح Django template filters إلى Jinja2 filters"""
    
    print("🔧 إصلاح Django template filters إلى Jinja2...")
    
    # إنشاء مجلد النسخ الاحتياطية
    backup_dir = "template_fix_backups"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    fixed_files = 0
    total_fixes = 0
    
    # البحث في جميع ملفات HTML
    for root, dirs, files in os.walk("templates"):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    
                    # إصلاح date filters
                    # |date:"%Y-%m-%d" → |strftime("%Y-%m-%d")
                    content = re.sub(r'\|date:"([^"]+)"', r'|strftime("\1")', content)
                    
                    # إصلاح floatformat filters
                    # |floatformat:2 → |round(2)
                    content = re.sub(r'\|floatformat:(\d+)', r'|round(\1)', content)
                    
                    # إصلاح default filters
                    # |default:0 → |default(0)
                    content = re.sub(r'\|default:(\w+)', r'|default(\1)', content)
                    
                    # إصلاح truncatechars filters
                    # |truncatechars:50 → |truncate(50)
                    content = re.sub(r'\|truncatechars:(\d+)', r'|truncate(\1)', content)
                    
                    # إصلاح length filters
                    # |length → |length
                    # هذا صحيح في Jinja2
                    
                    if content != original_content:
                        # إنشاء نسخة احتياطية
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        backup_file = os.path.join(backup_dir, f"{file}_{timestamp}.backup")
                        shutil.copy2(file_path, backup_file)
                        
                        # حفظ الملف المُصلح
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        fixes_count = len(re.findall(r'\|strftime\(|\|round\(|\|default\(|\|truncate\(', content))
                        total_fixes += fixes_count
                        fixed_files += 1
                        
                        print(f"✅ تم إصلاح {file_path} - {fixes_count} إصلاح")
                
                except Exception as e:
                    print(f"❌ خطأ في معالجة {file_path}: {e}")
    
    print(f"\n🎉 تم الانتهاء!")
    print(f"📁 الملفات المُصلحة: {fixed_files}")
    print(f"🔧 إجمالي الإصلاحات: {total_fixes}")
    print(f"💾 النسخ الاحتياطية في: {backup_dir}")

if __name__ == "__main__":
    fix_jinja_filters()
