{% extends "base.html" %} {% block title %}التنبيهات - نظام الشؤون القانونية{%
endblock %} {% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
  <h2><i class="fas fa-bell me-2"></i>التنبيهات</h2>
  <div>
    <button class="btn btn-outline-primary me-2" onclick="markAllAsRead()">
      <i class="fas fa-check-double me-1"></i>قراءة الكل
    </button>
    <button class="btn btn-primary" onclick="refreshNotifications()">
      <i class="fas fa-sync-alt me-1"></i>تحديث
    </button>
  </div>
</div>

<!-- إحصائيات التنبيهات -->
<div class="row mb-4">
  <div class="col-md-3">
    <div class="card bg-primary text-white">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h5 class="card-title">إجمالي التنبيهات</h5>
            <h3 id="totalNotifications">0</h3>
          </div>
          <div class="align-self-center">
            <i class="fas fa-bell fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-warning text-white">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h5 class="card-title">غير مقروءة</h5>
            <h3 id="unreadNotifications">0</h3>
          </div>
          <div class="align-self-center">
            <i class="fas fa-exclamation-circle fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-success text-white">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h5 class="card-title">مقروءة</h5>
            <h3 id="readNotifications">0</h3>
          </div>
          <div class="align-self-center">
            <i class="fas fa-check-circle fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-info text-white">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h5 class="card-title">اليوم</h5>
            <h3 id="todayNotifications">0</h3>
          </div>
          <div class="align-self-center">
            <i class="fas fa-calendar-day fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- فلاتر التنبيهات -->
<div class="card mb-4">
  <div class="card-body">
    <div class="row">
      <div class="col-md-3">
        <label class="form-label">نوع التنبيه</label>
        <select class="form-select" id="notificationTypeFilter">
          <option value="">جميع الأنواع</option>
          <option value="appointment">المواعيد</option>
          <option value="court">جلسات المحكمة</option>
          <option value="document">المستندات</option>
          <option value="payment">المدفوعات</option>
          <option value="system">النظام</option>
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">الحالة</label>
        <select class="form-select" id="notificationStatusFilter">
          <option value="">جميع الحالات</option>
          <option value="unread">غير مقروءة</option>
          <option value="read">مقروءة</option>
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">التاريخ</label>
        <select class="form-select" id="notificationDateFilter">
          <option value="">جميع التواريخ</option>
          <option value="today">اليوم</option>
          <option value="yesterday">أمس</option>
          <option value="week">هذا الأسبوع</option>
          <option value="month">هذا الشهر</option>
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">&nbsp;</label>
        <div>
          <button class="btn btn-primary w-100" onclick="applyFilters()">
            <i class="fas fa-filter me-1"></i>تطبيق الفلاتر
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- قائمة التنبيهات -->
<div class="card">
  <div class="card-header">
    <h5 class="mb-0">قائمة التنبيهات</h5>
  </div>
  <div class="card-body p-0">
    <div id="notificationsList">
      <!-- سيتم تحميل التنبيهات هنا -->
      <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحميل التنبيهات...</p>
      </div>
    </div>
  </div>
</div>

<!-- Modal لتفاصيل التنبيه -->
<div class="modal fade" id="notificationModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تفاصيل التنبيه</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <div id="notificationDetails">
          <!-- تفاصيل التنبيه -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          إغلاق
        </button>
        <button
          type="button"
          class="btn btn-primary"
          onclick="markAsReadAndClose()"
        >
          <i class="fas fa-check me-1"></i>وضع علامة قراءة
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  let currentNotifications = [];
  let selectedNotificationId = null;

  // تحميل التنبيهات عند تحميل الصفحة
  document.addEventListener("DOMContentLoaded", function () {
    loadNotifications();
  });

  function loadNotifications() {
    fetch("/api/notifications/get")
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          currentNotifications = data.notifications;
          displayNotifications(currentNotifications);
          updateStats();
        } else {
          showAlert("error", "حدث خطأ في تحميل التنبيهات");
        }
      })
      .catch((error) => {
        console.error("Error:", error);
        showAlert("error", "حدث خطأ في الاتصال بالخادم");
      });
  }

  function displayNotifications(notifications) {
    const container = document.getElementById("notificationsList");

    if (notifications.length === = = = = 0) {
      container.innerHTML = `
      <div class="text-center py-5">
        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">لا توجد تنبيهات</h5>
        <p class="text-muted">لم يتم العثور على أي تنبيهات</p>
      </div>
    `;
      return;
    }

    let html = "";
    notifications.forEach((notification) => {
      const iconClass = getNotificationIcon(notification.type);
      const bgClass = getNotificationBgClass(notification.type);
      const readClass = notification.is_read ? "" : "notification-unread";

      html += `
      <div class="notification-item-full ${readClass}" data-id="${
        notification.id
      }">
        <div class="d-flex p-3 border-bottom">
          <div class="notification-icon ${bgClass} me-3">
            <i class="fas fa-${iconClass} text-white"></i>
          </div>
          <div class="flex-grow-1">
            <div class="d-flex justify-content-between align-items-start">
              <div>
                <h6 class="mb-1">${notification.title}</h6>
                <p class="mb-1 text-muted">${notification.message}</p>
                <small class="text-muted">
                  <i class="fas fa-clock me-1"></i>${notification.time}
                </small>
              </div>
              <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                  <i class="fas fa-ellipsis-v"></i>
                </button>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="#" onclick="viewNotification(${
                    notification.id
                  })">
                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                  </a></li>
                  ${
                    !notification.is_read
                      ? `
                  <li><a class="dropdown-item" href="#" onclick="markSingleAsRead(${notification.id})">
                    <i class="fas fa-check me-2"></i>وضع علامة قراءة
                  </a></li>
                  `
                      : ""
                  }
                  <li><a class="dropdown-item text-danger" href="#" onclick="deleteNotification(${
                    notification.id
                  })">
                    <i class="fas fa-trash me-2"></i>حذف
                  </a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
    });

    container.innerHTML = html;
  }

  function getNotificationIcon(type) {
    switch (type) {
      case "appointment":
        return "calendar-alt";
      case "court":
        return "exclamation-triangle";
      case "document":
        return "file-alt";
      case "payment":
        return "dollar-sign";
      case "system":
        return "cog";
      default:
        return "info-circle";
    }
  }

  function getNotificationBgClass(type) {
    switch (type) {
      case "appointment":
        return "bg-primary";
      case "court":
        return "bg-warning";
      case "document":
        return "bg-success";
      case "payment":
        return "bg-info";
      case "system":
        return "bg-secondary";
      default:
        return "bg-primary";
    }
  }

  function updateStats() {
    const total = currentNotifications.length;
    const unread = currentNotifications.filter((n) => !n.is_read).length;
    const read = total - unread;

    document.getElementById("totalNotifications").textContent = total;
    document.getElementById("unreadNotifications").textContent = unread;
    document.getElementById("readNotifications").textContent = read;
    document.getElementById("todayNotifications").textContent = total; // عدد التنبيهات اليوم
  }

  function refreshNotifications() {
    showAlert("info", "جاري تحديث التنبيهات...");
    loadNotifications();
  }

  function markAllAsRead() {
    fetch("/api/notifications/mark-all-read", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          currentNotifications.forEach((n) => (n.is_read = true));
          displayNotifications(currentNotifications);
          updateStats();
          showAlert("success", "تم وضع علامة قراءة على جميع التنبيهات");
        } else {
          showAlert("error", data.message);
        }
      })
      .catch((error) => {
        showAlert("error", "حدث خطأ في الاتصال بالخادم");
      });
  }

  function markSingleAsRead(id) {
    fetch("/api/notifications/mark-read", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ notification_id: id }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          const notification = currentNotifications.find((n) => n.id === = = = = id);
          if (notification) {
            notification.is_read = true;
            displayNotifications(currentNotifications);
            updateStats();
            showAlert("success", "تم وضع علامة قراءة على التنبيه");
          }
        } else {
          showAlert("error", data.message);
        }
      })
      .catch((error) => {
        showAlert("error", "حدث خطأ في الاتصال بالخادم");
      });
  }

  function viewNotification(id) {
    const notification = currentNotifications.find((n) => n.id === = = = = id);
    if (notification) {
      selectedNotificationId = id;
      document.getElementById("notificationDetails").innerHTML = `
      <div class="notification-detail">
        <div class="d-flex mb-3">
          <div class="notification-icon ${getNotificationBgClass(
            notification.type
          )} me-3">
            <i class="fas fa-${getNotificationIcon(
              notification.type
            )} text-white"></i>
          </div>
          <div>
            <h5>${notification.title}</h5>
            <p class="text-muted mb-0">${notification.time}</p>
          </div>
        </div>
        <div class="notification-message">
          <p>${notification.message}</p>
        </div>
        <div class="notification-status">
          <span class="badge ${
            notification.is_read ? "bg-success" : "bg-warning"
          }">
            ${notification.is_read ? "مقروء" : "غير مقروء"}
          </span>
        </div>
      </div>
    `;

      const modal = new bootstrap.Modal(
        document.getElementById("notificationModal")
      );
      modal.show();
    }
  }

  function markAsReadAndClose() {
    if (selectedNotificationId) {
      markSingleAsRead(selectedNotificationId);
      const modal = bootstrap.Modal.getInstance(
        document.getElementById("notificationModal")
      );
      modal.hide();
    }
  }

  function deleteNotification(id) {
    if (confirm("هل أنت متأكد من حذف هذا التنبيه؟")) {
      // مؤقتاً سنحذف من القائمة المحلية
      currentNotifications = currentNotifications.filter((n) => n.id !== == === === = === = = id);
      displayNotifications(currentNotifications);
      updateStats();
      showAlert("success", "تم حذف التنبيه");
    }
  }

  function applyFilters() {
    const typeFilter = document.getElementById("notificationTypeFilter").value;
    const statusFilter = document.getElementById(
      "notificationStatusFilter"
    ).value;
    const dateFilter = document.getElementById("notificationDateFilter").value;

    let filtered = [...currentNotifications];

    if (typeFilter) {
      filtered = filtered.filter((n) => n.type === = = = = typeFilter);
    }

    if (statusFilter) {
      if (statusFilter === = = = = "read") {
        filtered = filtered.filter((n) => n.is_read);
      } else if (statusFilter === = = = = "unread") {
        filtered = filtered.filter((n) => !n.is_read);
      }
    }

    displayNotifications(filtered);
    showAlert("info", `تم تطبيق الفلاتر - ${filtered.length} تنبيه`);
  }

  // دالة لإظهار التنبيهات
  function showAlert(type, message) {
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText =
      "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";

    const icon =
      type === = = = = "success"
        ? "check-circle"
        : type === = = = = "error"
        ? "exclamation-triangle"
        : type === = = = = "warning"
        ? "exclamation-circle"
        : "info-circle";

    alertDiv.innerHTML = `
      <i class="fas fa-${icon} me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }
</script>

<style>
  .notification-item-full {
    transition: all 0.3s ease;
  }

  .notification-item-full:hover {
    background-color: #f8f9fa;
  }

  .notification-unread {
    background-color: #e3f2fd;
    border-left: 4px solid var(--primary-color);
  }

  .notification-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .notification-detail .notification-icon {
    width: 60px;
    height: 60px;
  }
</style>
{% endblock %}
