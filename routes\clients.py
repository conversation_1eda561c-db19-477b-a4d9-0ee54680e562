from django.shortcuts import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, send_file
from flask_login import login_required, current_user
from datetime import datetime
from sqlalchemy import or_, func
import uuid
import os
from werkzeug.utils import secure_filename
from permissions_manager import permission_required, any_permission_required

bp = Blueprint('clients', __name__, url_prefix='/clients')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from django.shortcuts import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        return {}

def get_db():
    """الحصول على كائن قاعدة البيانات"""
    try:
        from django.shortcuts import current_app
        return current_app.extensions['sqlalchemy'].db
    except (RuntimeError, KeyError):
        return None

def allowed_file(filename):
    """التحقق من أن نوع الملف مسموح"""
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_file_size(file_path):
    """الحصول على حجم الملف"""
    try:
        return os.path.getsize(file_path)
    except:
        return 0

def ensure_upload_folder():
    """التأكد من وجود مجلد الرفع"""
    upload_folder = os.path.join(current_app.root_path, 'uploads', 'clients')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    return upload_folder

@bp.route('/')
@login_required
@permission_required('view_clients')
def index():
    """عرض جميع العملاء"""
    try:
        models = get_models()
        Client = models.get('Client')
        Case = models.get('Case')
        Invoice = models.get('Invoice')
        db = get_db()

        if not all([Client, Case, Invoice, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # معاملات البحث والفلترة
        page = request.GET.get('page', 1, type=int)
        search = request.GET.get('search', '')
        client_type = request.GET.get('client_type', '')
        status_filter = request.GET.get('status', '')
        sort_by = request.GET.get('sort', 'created_at')
        sort_order = request.GET.get('order', 'desc')

        # بناء الاستعلام الأساسي
        query = Client.query

        # البحث النصي
        if search:
            query = query.filter(
                or_(
                    Client.full_name.contains(search),
                    Client.email.contains(search),
                    Client.phone.contains(search),
                    Client.client_code.contains(search),
                    Client.national_id.contains(search)
                )
            )

        # فلترة حسب نوع العميل
        if client_type:
            query = query.filter(Client.client_type == client_type)

        # فلترة حسب الحالة
        if status_filter == 'active':
            query = query.filter(Client.is_active == True)
        elif status_filter == 'inactive':
            query = query.filter(Client.is_active == False)

        # ترتيب النتائج
        if sort_by == 'name':
            if sort_order == 'asc':
                query = query.order_by(Client.full_name.asc())
            else:
                query = query.order_by(Client.full_name.desc())
        elif sort_by == 'created_at':
            if sort_order == 'asc':
                query = query.order_by(Client.created_at.asc())
            else:
                query = query.order_by(Client.created_at.desc())
        elif sort_by == 'client_code':
            if sort_order == 'asc':
                query = query.order_by(Client.client_code.asc())
            else:
                query = query.order_by(Client.client_code.desc())

        # تطبيق pagination
        per_page = 10
        clients = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # إضافة إحصائيات لكل عميل
        for client in clients.items:
            # عدد القضايا
            client.cases_count = Case.query.filter_by(client_id=client.id).count()

            # إجمالي قيمة الفواتير
            total_invoices = db.query(func.sum(Invoice.total_amount)).filter_by(client_id=client.id).scalar()
            client.total_value = float(total_invoices) if total_invoices else 0.0

        # إحصائيات عامة
        stats = {
            'total': Client.query.count(),
            'active': Client.query.filter_by(is_active=True).count(),
            'inactive': Client.query.filter_by(is_active=False).count(),
            'individuals': Client.query.filter_by(client_type='individual').count(),
            'companies': Client.query.filter_by(client_type='company').count(),
            'organizations': Client.query.filter_by(client_type='organization').count()
        }

        return render('clients/index.html',
                             clients=clients,
                             search=search,
                             client_type=client_type,
                             status_filter=status_filter,
                             sort_by=sort_by,
                             sort_order=sort_order,
                             stats=stats)

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('create_clients')
def add():
    """إضافة عميل جديد"""
    try:
        models = get_models()
        Client = models.get('Client')
        db = get_db()

        if not all([Client, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('clients.index'))

        if request.method == 'POST':
            # جلب البيانات من النموذج
            full_name = request.POST.get('full_name', '').strip()
            email = request.POST.get('email', '').strip()
            phone = request.POST.get('phone', '').strip()
            address = request.POST.get('address', '').strip()
            national_id = request.POST.get('national_id', '').strip()
            client_type = request.POST.get('client_type', 'individual')
            notes = request.POST.get('notes', '').strip()

            # التحقق من البيانات المطلوبة
            if not full_name:
                flash('اسم العميل مطلوب', 'error')
                return render('clients/add.html')

            # التحقق من عدم تكرار البريد الإلكتروني
            if email and Client.query.filter_by(email=email).first():
                flash('البريد الإلكتروني مستخدم بالفعل', 'error')
                return render('clients/add.html')

            # التحقق من عدم تكرار رقم الهوية
            if national_id and Client.query.filter_by(national_id=national_id).first():
                flash('رقم الهوية مستخدم بالفعل', 'error')
                return render('clients/add.html')

            # إنشاء رمز العميل
            client_code = f"CLIENT-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

            # إنشاء العميل الجديد
            new_client = Client(
                client_code=client_code,
                full_name=full_name,
                email=email if email else None,
                phone=phone if phone else None,
                address=address if address else None,
                national_id=national_id if national_id else None,
                client_type=client_type,
                notes=notes if notes else None,
                is_active=True
            )

            db.add(new_client)
            db.save()

            flash('تم إضافة العميل بنجاح', 'success')
            return redirect(url_for('clients.view', id=new_client.id))

        return render('clients/add.html')

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('clients.index'))

@bp.route('/<int:id>')
@login_required
@permission_required('view_clients')
def view(id):
    """عرض تفاصيل العميل"""
    try:
        models = get_models()
        Client = models.get('Client')
        Case = models.get('Case')
        Invoice = models.get('Invoice')
        Appointment = models.get('Appointment')
        Contract = models.get('Contract')
        ClientFile = models.get('ClientFile')
        db = get_db()

        if not all([Client, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('clients.index'))

        # جلب العميل
        client = Client.query.get_or_404(id)

        # جلب قضايا العميل
        cases = Case.query.filter_by(client_id=id).order_by(Case.created_at.desc()).all() if Case else []

        # جلب فواتير العميل
        invoices = Invoice.query.filter_by(client_id=id).order_by(Invoice.created_at.desc()).limit(10).all() if Invoice else []

        # جلب مواعيد العميل
        appointments = Appointment.query.filter_by(client_id=id).order_by(Appointment.appointment_date.desc()).limit(10).all() if Appointment else []

        # جلب عقود العميل
        contracts = Contract.query.filter_by(client_id=id).order_by(Contract.created_at.desc()).all() if Contract else []

        # جلب ملفات العميل (آخر 5 ملفات)
        client_files = ClientFile.query.filter_by(client_id=id).order_by(ClientFile.created_at.desc()).limit(5).all() if ClientFile else []

        # حساب الإحصائيات
        stats = {
            'cases_count': len(cases),
            'active_cases': len([c for c in cases if c.case_status == 'active']),
            'total_invoices': len(invoices),
            'total_amount': sum([float(inv.total_amount) for inv in invoices]),
            'paid_amount': sum([float(inv.paid_amount) for inv in invoices if inv.paid_amount]),
            'pending_amount': sum([float(inv.total_amount - (inv.paid_amount or 0)) for inv in invoices]),
            'appointments_count': len(appointments),
            'contracts_count': len(contracts),
            'active_contracts': len([c for c in contracts if c.status == 'active']),
            'files_count': len(client_files)
        }

        return render('clients/view.html',
                             client=client,
                             cases=cases,
                             invoices=invoices,
                             appointments=appointments,
                             contracts=contracts,
                             client_files=client_files,
                             stats=stats)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('clients.index'))

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('edit_clients')
def edit(id):
    """تعديل العميل"""
    try:
        models = get_models()
        Client = models.get('Client')
        db = get_db()

        if not all([Client, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('clients.index'))

        client = Client.query.get_or_404(id)

        if request.method == 'POST':
            # جلب البيانات من النموذج
            full_name = request.POST.get('full_name', '').strip()
            email = request.POST.get('email', '').strip()
            phone = request.POST.get('phone', '').strip()
            address = request.POST.get('address', '').strip()
            national_id = request.POST.get('national_id', '').strip()
            client_type = request.POST.get('client_type', 'individual')
            notes = request.POST.get('notes', '').strip()
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من البيانات المطلوبة
            if not full_name:
                flash('اسم العميل مطلوب', 'error')
                return render('clients/edit.html', client=client)

            # التحقق من عدم تكرار البريد الإلكتروني (إذا تم تغييره)
            if email and email != client.email:
                existing_client = Client.query.filter_by(email=email).first()
                if existing_client:
                    flash('البريد الإلكتروني مستخدم بالفعل', 'error')
                    return render('clients/edit.html', client=client)

            # التحقق من عدم تكرار رقم الهوية (إذا تم تغييره)
            if national_id and national_id != client.national_id:
                existing_client = Client.query.filter_by(national_id=national_id).first()
                if existing_client:
                    flash('رقم الهوية مستخدم بالفعل', 'error')
                    return render('clients/edit.html', client=client)

            # تحديث بيانات العميل
            client.full_name = full_name
            client.email = email if email else None
            client.phone = phone if phone else None
            client.address = address if address else None
            client.national_id = national_id if national_id else None
            client.client_type = client_type
            client.notes = notes if notes else None
            client.is_active = is_active
            client.updated_at = datetime.utcnow()

            db.save()

            flash('تم تحديث العميل بنجاح', 'success')
            return redirect(url_for('clients.view', id=id))

        return render('clients/edit.html', client=client)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('clients.index'))

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@permission_required('delete_clients')
def delete(id):
    """حذف العميل (إلغاء تفعيل)"""
    try:
        models = get_models()
        Client = models.get('Client')
        db = get_db()

        if not all([Client, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        client = Client.query.get_or_404(id)

        # إلغاء تفعيل العميل بدلاً من حذفه نهائياً
        client.is_active = False
        client.updated_at = datetime.utcnow()

        db.save()

        return JsonResponse({'success': True, 'message': 'تم إلغاء تفعيل العميل بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:id>/activate', methods=['POST'])
@login_required
@permission_required('edit_clients')
def activate(id):
    """تفعيل العميل"""
    try:
        models = get_models()
        Client = models.get('Client')
        db = get_db()

        if not all([Client, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        client = Client.query.get_or_404(id)

        client.is_active = True
        client.updated_at = datetime.utcnow()

        db.save()

        return JsonResponse({'success': True, 'message': 'تم تفعيل العميل بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/api/search')
@login_required
def api_search():
    """البحث عن العملاء عبر API"""
    try:
        models = get_models()
        Client = models.get('Client')

        if not Client:
            return JsonResponse([])

        query = request.GET.get('q', '')
        limit = int(request.GET.get('limit', 10))

        if len(query) < 2:
            return JsonResponse([])

        # البحث في قاعدة البيانات
        clients = Client.query.filter(
            or_(
                Client.full_name.contains(query),
                Client.email.contains(query),
                Client.phone.contains(query),
                Client.client_code.contains(query)
            )
        ).filter_by(is_active=True).limit(limit).all()

        # تحويل النتائج إلى JSON
        results = []
        for client in clients:
            results.append({
                'id': client.id,
                'text': f'{client.full_name} ({client.client_code})',
                'email': client.email or '',
                'phone': client.phone or '',
                'client_type': client.client_type or 'individual'
            })

        return JsonResponse(results)
    except Exception as e:
        return JsonResponse({'error': str(e)}), 500

@bp.route('/api/stats')
@login_required
@permission_required('view_clients')
def api_stats():
    """إحصائيات العملاء عبر API"""
    try:
        models = get_models()
        Client = models.get('Client')
        Case = models.get('Case')
        Invoice = models.get('Invoice')
        db = get_db()

        if not all([Client, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        # إحصائيات أساسية
        total_clients = Client.query.count()
        active_clients = Client.query.filter_by(is_active=True).count()
        inactive_clients = Client.query.filter_by(is_active=False).count()

        # إحصائيات حسب النوع
        individuals = Client.query.filter_by(client_type='individual').count()
        companies = Client.query.filter_by(client_type='company').count()
        organizations = Client.query.filter_by(client_type='organization').count()

        # إحصائيات مالية (إذا كان نموذج الفواتير متاح)
        total_revenue = 0
        if Invoice:
            total_revenue = db.query(func.sum(Invoice.total_amount)).scalar() or 0

        stats = {
            'total': total_clients,
            'active': active_clients,
            'inactive': inactive_clients,
            'individuals': individuals,
            'companies': companies,
            'organizations': organizations,
            'total_revenue': float(total_revenue)
        }

        return JsonResponse({'success': True, 'stats': stats})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/export')
@login_required
@permission_required('view_clients')
def export():
    """تصدير قائمة العملاء"""
    try:
        # سيتم تنفيذ هذه الوظيفة لاحقاً
        flash('سيتم إضافة وظيفة التصدير قريباً', 'info')
        return redirect(url_for('clients.index'))
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('clients.index'))

@bp.route('/<int:id>/files')
@login_required
@permission_required('view_clients')
def files(id):
    """عرض ملفات العميل"""
    try:
        models = get_models()
        Client = models.get('Client')
        ClientFile = models.get('ClientFile')

        if not all([Client, ClientFile]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('clients.index'))

        client = Client.query.get_or_404(id)
        files = ClientFile.query.filter_by(client_id=id).order_by(ClientFile.created_at.desc()).all()

        return render('clients/files.html', client=client, files=files)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('clients.view', id=id))

@bp.route('/<int:id>/upload', methods=['POST'])
@login_required
@permission_required('manage_client_documents')
def upload_file(id):
    """رفع ملف للعميل"""
    try:
        models = get_models()
        Client = models.get('Client')
        ClientFile = models.get('ClientFile')
        db = get_db()

        if not all([Client, ClientFile, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        client = Client.query.get_or_404(id)

        if 'file' not in request.files:
            return JsonResponse({'success': False, 'message': 'لم يتم اختيار ملف'})

        file = request.files['file']
        description = request.POST.get('description', '')

        if file.filename == '':
            return JsonResponse({'success': False, 'message': 'لم يتم اختيار ملف'})

        if not allowed_file(file.filename):
            return JsonResponse({'success': False, 'message': 'نوع الملف غير مسموح'})

        # إنشاء مجلد الرفع
        upload_folder = ensure_upload_folder()

        # إنشاء اسم ملف آمن
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        file_path = os.path.join(upload_folder, unique_filename)

        # حفظ الملف
        file.save(file_path)

        # حفظ معلومات الملف في قاعدة البيانات
        client_file = ClientFile(
            client_id=id,
            filename=unique_filename,
            original_filename=filename,
            file_path=file_path,
            file_size=get_file_size(file_path),
            file_type=filename.rsplit('.', 1)[1].lower() if '.' in filename else '',
            description=description,
            uploaded_by=current_user.id
        )

        db.add(client_file)
        db.save()

        return JsonResponse({'success': True, 'message': 'تم رفع الملف بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:client_id>/files/<int:file_id>/download')
@login_required
@permission_required('view_clients')
def download_file(client_id, file_id):
    """تحميل ملف العميل"""
    try:
        models = get_models()
        ClientFile = models.get('ClientFile')

        if not ClientFile:
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('clients.view', id=client_id))

        client_file = ClientFile.query.filter_by(id=file_id, client_id=client_id).first_or_404()

        if not os.path.exists(client_file.file_path):
            flash('الملف غير موجود', 'error')
            return redirect(url_for('clients.files', id=client_id))

        return send_file(client_file.file_path,
                        as_attachment=True,
                        download_name=client_file.original_filename)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('clients.files', id=client_id))

@bp.route('/<int:client_id>/files/<int:file_id>/delete', methods=['POST'])
@login_required
@permission_required('manage_client_documents')
def delete_file(client_id, file_id):
    """حذف ملف العميل"""
    try:
        models = get_models()
        ClientFile = models.get('ClientFile')
        db = get_db()

        if not all([ClientFile, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        client_file = ClientFile.query.filter_by(id=file_id, client_id=client_id).first_or_404()

        # حذف الملف من النظام
        if os.path.exists(client_file.file_path):
            os.remove(client_file.file_path)

        # حذف السجل من قاعدة البيانات
        db.delete(client_file)
        db.save()

        return JsonResponse({'success': True, 'message': 'تم حذف الملف بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})
