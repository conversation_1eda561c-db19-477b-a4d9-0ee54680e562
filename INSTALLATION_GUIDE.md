# دليل التثبيت والتشغيل - نظام الشؤون القانونية

## 📋 المتطلبات الأساسية

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11, macOS, Linux
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM كحد أدنى
- **مساحة القرص**: 1 GB مساحة فارغة
- **متصفح الويب**: Chrome, Firefox, Safari, Edge (أحدث إصدار)

### التحقق من Python
```bash
python --version
# أو
python3 --version
```

## 🚀 خطوات التثبيت

### الخطوة 1: تحميل المشروع
```bash
# إذا كان لديك Git
git clone [repository-url]
cd AhmedSaeid

# أو قم بتحميل الملف المضغوط واستخراجه
```

### الخطوة 2: إنشاء بيئة افتراضية (اختياري ولكن مُوصى به)
```bash
# إنشاء البيئة الافتراضية
python -m venv legal_system_env

# تفعيل البيئة الافتراضية
# على Windows:
legal_system_env\Scripts\activate

# على macOS/Linux:
source legal_system_env/bin/activate
```

### الخطوة 3: تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### الخطوة 4: إعداد متغيرات البيئة
قم بتحرير ملف `.env` وتخصيص الإعدادات:
```env
SECRET_KEY=your-very-secret-key-change-in-production
DATABASE_URL=sqlite:///legal_system.db
FLASK_ENV=development
FLASK_DEBUG=True
```

### الخطوة 5: تشغيل التطبيق
```bash
python app.py
```

## 🌐 الوصول للنظام

1. افتح متصفح الويب
2. انتقل إلى: `http://localhost:5000`
3. استخدم بيانات الدخول الافتراضية:
   - **اسم المستخدم**: admin
   - **كلمة المرور**: admin123

## 🔧 إعدادات متقدمة

### تغيير منفذ الخادم
```python
# في ملف app.py، غيّر السطر الأخير إلى:
app.run(debug=True, host='0.0.0.0', port=8080)
```

### استخدام قاعدة بيانات PostgreSQL
1. تثبيت PostgreSQL
2. تثبيت psycopg2:
```bash
pip install psycopg2-binary
```
3. تحديث DATABASE_URL في ملف .env:
```env
DATABASE_URL=postgresql://username:password@localhost/legal_system
```

### تشغيل النظام في الإنتاج
```bash
# تثبيت Gunicorn
pip install gunicorn

# تشغيل التطبيق
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## 🛠️ استكشاف الأخطاء وإصلاحها

### مشكلة: "ModuleNotFoundError"
```bash
# تأكد من تثبيت جميع المتطلبات
pip install -r requirements.txt

# تأكد من تفعيل البيئة الافتراضية
```

### مشكلة: "Port already in use"
```bash
# تغيير المنفذ في app.py أو إيقاف العملية التي تستخدم المنفذ 5000
```

### مشكلة: "Database locked"
```bash
# إغلاق جميع اتصالات قاعدة البيانات وإعادة تشغيل التطبيق
```

### مشكلة: صفحة فارغة أو أخطاء CSS
```bash
# تأكد من وجود مجلد templates ومجلد static
# تحقق من مسارات الملفات في القوالب
```

## 📁 هيكل الملفات المطلوب

```
AhmedSaeid/
├── app.py                 ✅ موجود
├── models.py              ✅ موجود
├── requirements.txt       ✅ موجود
├── .env                   ✅ موجود
├── routes/                ✅ موجود
├── templates/             ✅ موجود
├── uploads/               📁 سيتم إنشاؤه تلقائياً
└── legal_system.db        📄 سيتم إنشاؤه تلقائياً
```

## 🔐 الأمان والحماية

### تغيير كلمة المرور الافتراضية
1. سجل دخول بالحساب الافتراضي
2. انتقل إلى إعدادات الحساب
3. غيّر كلمة المرور

### تحديث SECRET_KEY
```env
# في ملف .env، استخدم مفتاح قوي وفريد
SECRET_KEY=your-unique-secret-key-here
```

## 📊 النسخ الاحتياطي

### نسخ احتياطي لقاعدة البيانات
```bash
# نسخ ملف قاعدة البيانات
cp legal_system.db backup_legal_system_$(date +%Y%m%d).db
```

### نسخ احتياطي للملفات المرفوعة
```bash
# نسخ مجلد uploads
cp -r uploads backup_uploads_$(date +%Y%m%d)
```

## 🔄 التحديثات

### تحديث المتطلبات
```bash
pip install --upgrade -r requirements.txt
```

### تحديث قاعدة البيانات
```bash
# إذا تم إضافة جداول جديدة، قم بحذف قاعدة البيانات الحالية
# (تأكد من عمل نسخة احتياطية أولاً)
rm legal_system.db
python app.py
```

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:

1. **تحقق من الأخطاء**: راجع رسائل الخطأ في Terminal/Command Prompt
2. **راجع الملفات**: تأكد من وجود جميع الملفات المطلوبة
3. **تحقق من الإعدادات**: راجع ملف .env والإعدادات
4. **أعد التشغيل**: أغلق التطبيق وأعد تشغيله
5. **تواصل معنا**: أرسل تفاصيل المشكلة والخطأ

---

🎉 **تهانينا!** إذا اتبعت هذه الخطوات بنجاح، فإن نظام الشؤون القانونية جاهز للاستخدام!
