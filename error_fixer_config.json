{"auto_error_fixer_config": {"description": "إعدادات معالج الأخطاء التلقائي", "version": "1.0.0", "general_settings": {"create_backups": true, "backup_directory": "error_fix_backups", "log_level": "INFO", "max_backup_files": 50}, "template_fixes": {"enabled": true, "fix_flask_to_django": true, "fix_python_format": true, "fix_css_in_style_attrs": true, "fix_template_tags": true, "add_dynamic_js": true}, "python_fixes": {"enabled": true, "fix_imports": true, "fix_flask_syntax": true, "fix_database_calls": true, "modernize_syntax": false}, "javascript_fixes": {"enabled": true, "remove_console_logs": false, "remove_debugger": true, "modernize_syntax": true, "fix_equality_operators": true}, "database_fixes": {"enabled": true, "create_missing_indexes": true, "check_table_structure": true, "optimize_queries": false}, "file_patterns": {"templates": ["*.html", "*.htm"], "python": ["*.py"], "javascript": ["*.js"], "css": ["*.css"], "exclude_directories": ["venv", "__pycache__", ".git", "node_modules", "error_fix_backups"]}, "custom_fixes": {"template_patterns": {"url_for_fix": {"pattern": "url_for\\(['\"]([^'\"]+)['\"]\\)", "replacement": "{% url '$1' %}", "description": "تحويل Flask url_for إلى Django url"}, "format_fix": {"pattern": "\\{\\{\\s*['\"]([^'\"]*)['\"]\\s*\\.format\\(([^}]+)\\)\\s*\\}\\}", "replacement": "{{ $2|$1 }}", "description": "تحويل Python format إلى Django filter"}}, "python_patterns": {"flask_import_fix": {"pattern": "from flask import", "replacement": "from django.shortcuts import", "description": "تحويل استيراد Flask إلى Django"}}, "javascript_patterns": {"var_to_let": {"pattern": "var\\s+", "replacement": "let ", "description": "تحويل var إلى let"}}}, "reporting": {"generate_detailed_report": true, "include_file_list": true, "include_error_details": true, "save_report_to_file": true, "report_format": "text"}, "advanced_settings": {"parallel_processing": false, "max_file_size_mb": 10, "encoding": "utf-8", "line_ending": "auto", "preserve_timestamps": true}}}