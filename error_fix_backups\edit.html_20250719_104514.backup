{% extends "base.html" %}

{% block title %}تعديل الموظف: {{ employee.full_name }} - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>
        تعديل الموظف: {{ employee.full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لتفاصيل الموظف
            </a>
            <a href="{{ url_for('employees.index') }}" class="btn btn-outline-info">
                <i class="fas fa-list me-1"></i>
                قائمة الموظفين
            </a>
        </div>
    </div>
</div>

<form method="POST" id="employeeForm">
    <div class="row">
        <div class="col-md-8">
            <!-- البيانات الشخصية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        البيانات الشخصية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="{{ employee.full_name }}" required>
                                <div class="form-text">أدخل الاسم الكامل للموظف</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name_en" class="form-label">الاسم بالإنجليزية</label>
                                <input type="text" class="form-control" id="full_name_en" name="full_name_en" 
                                       value="{{ employee.full_name_en or '' }}">
                                <div class="form-text">الاسم باللغة الإنجليزية (اختياري)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="national_id" class="form-label">رقم الهوية الوطنية</label>
                                <input type="text" class="form-control" id="national_id" name="national_id" 
                                       value="{{ employee.national_id or '' }}" maxlength="10">
                                <div class="form-text">رقم الهوية الوطنية (10 أرقام)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date" 
                                       value="{{ employee.birth_date.strftime('%Y-%m-%d') if employee.birth_date else '' }}">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="gender" class="form-label">الجنس</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">اختر الجنس</option>
                                    <option value="male" {{ 'selected' if employee.gender == 'male' }}>ذكر</option>
                                    <option value="female" {{ 'selected' if employee.gender == 'female' }}>أنثى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="nationality" class="form-label">الجنسية</label>
                                <input type="text" class="form-control" id="nationality" name="nationality" 
                                       value="{{ employee.nationality or 'سعودي' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="marital_status" class="form-label">الحالة الاجتماعية</label>
                                <select class="form-select" id="marital_status" name="marital_status">
                                    <option value="">اختر الحالة</option>
                                    <option value="single" {{ 'selected' if employee.marital_status == 'single' }}>أعزب</option>
                                    <option value="married" {{ 'selected' if employee.marital_status == 'married' }}>متزوج</option>
                                    <option value="divorced" {{ 'selected' if employee.marital_status == 'divorced' }}>مطلق</option>
                                    <option value="widowed" {{ 'selected' if employee.marital_status == 'widowed' }}>أرمل</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الاتصال -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        معلومات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ employee.email or '' }}">
                                <div class="form-text">سيتم استخدامه للتواصل الرسمي</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ employee.phone or '' }}" placeholder="+966501234567">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mobile" class="form-label">رقم الجوال</label>
                                <input type="tel" class="form-control" id="mobile" name="mobile" 
                                       value="{{ employee.mobile or '' }}" placeholder="+966501234567">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3" 
                                  placeholder="أدخل العنوان الكامل للموظف">{{ employee.address or '' }}</textarea>
                    </div>
                </div>
            </div>

            <!-- البيانات الوظيفية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase me-2"></i>
                        البيانات الوظيفية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department_id" class="form-label">القسم <span class="text-danger">*</span></label>
                                <select class="form-select" id="department_id" name="department_id" required>
                                    <option value="">اختر القسم</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}" {{ 'selected' if employee.department_id == dept.id }}>{{ dept.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="position" class="form-label">المنصب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="position" name="position" 
                                       value="{{ employee.position or '' }}" required>
                                <div class="form-text">المسمى الوظيفي للموظف</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="position_en" class="form-label">المنصب بالإنجليزية</label>
                                <input type="text" class="form-control" id="position_en" name="position_en" 
                                       value="{{ employee.position_en or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employment_type" class="form-label">نوع التوظيف <span class="text-danger">*</span></label>
                                <select class="form-select" id="employment_type" name="employment_type" required>
                                    <option value="">اختر نوع التوظيف</option>
                                    <option value="full_time" {{ 'selected' if employee.employment_type == 'full_time' }}>دوام كامل</option>
                                    <option value="part_time" {{ 'selected' if employee.employment_type == 'part_time' }}>دوام جزئي</option>
                                    <option value="contract" {{ 'selected' if employee.employment_type == 'contract' }}>عقد</option>
                                    <option value="intern" {{ 'selected' if employee.employment_type == 'intern' }}>متدرب</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="employment_status" class="form-label">حالة التوظيف</label>
                                <select class="form-select" id="employment_status" name="employment_status">
                                    <option value="active" {{ 'selected' if employee.employment_status == 'active' }}>نشط</option>
                                    <option value="inactive" {{ 'selected' if employee.employment_status == 'inactive' }}>غير نشط</option>
                                    <option value="terminated" {{ 'selected' if employee.employment_status == 'terminated' }}>منتهي الخدمة</option>
                                    <option value="resigned" {{ 'selected' if employee.employment_status == 'resigned' }}>مستقيل</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="hire_date" class="form-label">تاريخ التوظيف <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date" 
                                       value="{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '' }}" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="termination_date" class="form-label">تاريخ إنهاء الخدمة</label>
                                <input type="date" class="form-control" id="termination_date" name="termination_date" 
                                       value="{{ employee.termination_date.strftime('%Y-%m-%d') if employee.termination_date else '' }}">
                                <div class="form-text">فقط في حالة إنهاء الخدمة</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الموظف</label>
                                <input type="text" class="form-control" value="{{ employee.employee_number }}" readonly>
                                <div class="form-text">رقم الموظف لا يمكن تغييره</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- البيانات المالية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        البيانات المالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="basic_salary" class="form-label">الراتب الأساسي</label>
                                <input type="number" class="form-control" id="basic_salary" name="basic_salary" 
                                       step="0.01" min="0" value="{{ employee.basic_salary or '' }}" placeholder="0.00">
                                <div class="form-text">بالريال السعودي</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="allowances" class="form-label">البدلات</label>
                                <input type="number" class="form-control" id="allowances" name="allowances"
                                       step="0.01" min="0" value="{{ employee.allowances or '' }}" placeholder="0.00">
                                <div class="form-text">إجمالي البدلات</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="deductions" class="form-label">الخصومات</label>
                                <input type="number" class="form-control" id="deductions" name="deductions"
                                       step="0.01" min="0" value="{{ employee.deductions or '' }}" placeholder="0.00">
                                <div class="form-text">إجمالي الخصومات</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="total_salary" class="form-label">إجمالي الراتب</label>
                                <input type="number" class="form-control" id="total_salary" name="total_salary" 
                                       value="{{ employee.total_salary or '' }}" readonly>
                                <div class="form-text">يحسب تلقائياً</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_account" class="form-label">رقم الحساب البنكي</label>
                                <input type="text" class="form-control" id="bank_account" name="bank_account" 
                                       value="{{ employee.bank_account or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_name" class="form-label">اسم البنك</label>
                                <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                       value="{{ employee.bank_name or '' }}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- معلومات الموظف -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الموظف
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>رقم الموظف:</strong></td>
                            <td>{{ employee.employee_number }}</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الإنشاء:</strong></td>
                            <td>{{ employee.created_at.strftime('%Y-%m-%d') if employee.created_at else 'غير محدد' }}</td>
                        </tr>
                        {% if employee.updated_at %}
                        <tr>
                            <td><strong>آخر تحديث:</strong></td>
                            <td>{{ employee.updated_at.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>

            <!-- إعدادات الحساب -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات الحساب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                               {{ 'checked' if employee.is_active }}>
                        <label class="form-check-label" for="is_active">
                            حساب نشط
                        </label>
                        <div class="form-text">إلغاء التفعيل يمنع الموظف من الوصول للنظام</div>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                        <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        {% if employee.employment_status == 'active' %}
                        <button type="button" onclick="terminateEmployee()" class="btn btn-outline-danger">
                            <i class="fas fa-user-times me-1"></i>
                            إنهاء الخدمة
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
// التحقق من صحة النموذج
document.getElementById('employeeForm').addEventListener('submit', function(e) {
    const fullName = document.getElementById('full_name').value.trim();
    const departmentId = document.getElementById('department_id').value;
    const position = document.getElementById('position').value.trim();
    const employmentType = document.getElementById('employment_type').value;
    const hireDate = document.getElementById('hire_date').value;
    
    if (!fullName || !departmentId || !position || !employmentType || !hireDate) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    const email = document.getElementById('email').value.trim();
    if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('يرجى إدخال بريد إلكتروني صحيح');
            document.getElementById('email').focus();
            return false;
        }
    }
    
    // التحقق من رقم الهوية إذا تم إدخاله
    const nationalId = document.getElementById('national_id').value.trim();
    if (nationalId && nationalId.length !== 10) {
        e.preventDefault();
        alert('رقم الهوية يجب أن يكون 10 أرقام');
        document.getElementById('national_id').focus();
        return false;
    }
});

// حساب إجمالي الراتب تلقائياً
function calculateTotalSalary() {
    const basicSalary = parseFloat(document.getElementById('basic_salary').value) || 0;
    const allowances = parseFloat(document.getElementById('allowances').value) || 0;
    const deductions = parseFloat(document.getElementById('deductions').value) || 0;
    const totalSalary = basicSalary + allowances - deductions;
    document.getElementById('total_salary').value = totalSalary.toFixed(2);
}

document.getElementById('basic_salary').addEventListener('input', calculateTotalSalary);
document.getElementById('allowances').addEventListener('input', calculateTotalSalary);
document.getElementById('deductions').addEventListener('input', calculateTotalSalary);

// إنهاء خدمة الموظف
function terminateEmployee() {
    if (confirm('هل أنت متأكد من إنهاء خدمة هذا الموظف؟')) {
        document.getElementById('employment_status').value = 'terminated';
        document.getElementById('termination_date').value = new Date().toISOString().split('T')[0];
        document.getElementById('is_active').checked = false;
        alert('تم تحديث حالة الموظف إلى "منتهي الخدمة". اضغط "حفظ التغييرات" لتأكيد العملية.');
    }
}

// حساب إجمالي الراتب عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateTotalSalary();
});
</script>
{% endblock %}
