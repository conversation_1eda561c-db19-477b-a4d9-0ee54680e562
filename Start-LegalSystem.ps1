# نظام الشؤون القانونية - سكريبت PowerShell للتشغيل المتقدم
# Legal Affairs Management System - Advanced PowerShell Startup Script

param(
    [string]$ConfigFile = "server_config.json",
    [switch]$OpenBrowser,
    [switch]$ShowNetworkInfo,
    [switch]$SetupFirewall
)

# إعداد الترميز للعربية
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "================================================================================" -ForegroundColor Cyan
Write-Host "                        نظام الشؤون القانونية" -ForegroundColor Yellow
Write-Host "                     Legal Affairs Management System" -ForegroundColor Yellow
Write-Host "================================================================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من وجود Python
Write-Host "🔧 جاري التحقق من متطلبات النظام..." -ForegroundColor Green

try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python متوفر: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python غير مثبت على النظام" -ForegroundColor Red
    Write-Host "يرجى تثبيت Python 3.7 أو أحدث من: https://python.org" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# التحقق من وجود ملف التطبيق
if (-not (Test-Path "app.py")) {
    Write-Host "❌ ملف app.py غير موجود" -ForegroundColor Red
    Write-Host "تأكد من وجودك في مجلد النظام الصحيح" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✅ ملفات النظام موجودة" -ForegroundColor Green

# قراءة إعدادات الخادم
$serverConfig = @{
    host = "0.0.0.0"
    port = 5000
    debug = $true
}

if (Test-Path $ConfigFile) {
    Write-Host "📖 قراءة إعدادات الخادم من $ConfigFile..." -ForegroundColor Blue
    try {
        $configContent = Get-Content $ConfigFile -Raw | ConvertFrom-Json
        $serverConfig.host = $configContent.host
        $serverConfig.port = $configContent.port
        $serverConfig.debug = $configContent.debug
    } catch {
        Write-Host "⚠️ خطأ في قراءة ملف الإعدادات، سيتم استخدام الإعدادات الافتراضية" -ForegroundColor Yellow
    }
}

# الحصول على معلومات الشبكة
$localIP = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "Wi-Fi*", "Ethernet*" | Where-Object {$_.IPAddress -notlike "169.254.*" -and $_.IPAddress -ne "127.0.0.1"} | Select-Object -First 1).IPAddress
if (-not $localIP) {
    $localIP = "127.0.0.1"
}

$protocol = if ($configContent.security_settings.enable_https) { "https" } else { "http" }

# عرض معلومات الشبكة
if ($ShowNetworkInfo -or $true) {
    Write-Host ""
    Write-Host "🌐 معلومات الشبكة:" -ForegroundColor Cyan
    Write-Host "================================================================================" -ForegroundColor Cyan
    Write-Host "🖥️  عنوان IP المحلي: $localIP" -ForegroundColor White
    Write-Host "🔌 المنفذ المكون: $($serverConfig.port)" -ForegroundColor White
    Write-Host "🔒 البروتوكول: $protocol" -ForegroundColor White
    Write-Host ""
    Write-Host "📍 روابط الوصول:" -ForegroundColor Yellow
    Write-Host "   المحلي:        ${protocol}://localhost:$($serverConfig.port)" -ForegroundColor Green
    Write-Host "   من الشبكة:     ${protocol}://${localIP}:$($serverConfig.port)" -ForegroundColor Green
    Write-Host ""
    Write-Host "🔐 بيانات الدخول الافتراضية:" -ForegroundColor Yellow
    Write-Host "   اسم المستخدم: admin" -ForegroundColor White
    Write-Host "   كلمة المرور:   admin123" -ForegroundColor White
    Write-Host ""
}

# إعداد جدار الحماية
if ($SetupFirewall) {
    Write-Host "🛡️ إعداد جدار الحماية..." -ForegroundColor Blue
    try {
        $ruleName = "Legal_System_Port_$($serverConfig.port)"
        $existingRule = Get-NetFirewallRule -DisplayName $ruleName -ErrorAction SilentlyContinue
        
        if ($existingRule) {
            Write-Host "✅ قاعدة جدار الحماية موجودة بالفعل" -ForegroundColor Green
        } else {
            New-NetFirewallRule -DisplayName $ruleName -Direction Inbound -Protocol TCP -LocalPort $serverConfig.port -Action Allow
            Write-Host "✅ تم إنشاء قاعدة جدار الحماية بنجاح" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️ فشل في إعداد جدار الحماية. قد تحتاج لتشغيل PowerShell كمدير" -ForegroundColor Yellow
        Write-Host "يمكنك إعداده يدوياً بالأمر:" -ForegroundColor Yellow
        Write-Host "netsh advfirewall firewall add rule name=`"Legal_System`" dir=in action=allow protocol=TCP localport=$($serverConfig.port)" -ForegroundColor Gray
    }
}

Write-Host "💡 نصائح مهمة:" -ForegroundColor Yellow
Write-Host "   • لتغيير إعدادات الخادم: سجل دخول كمدير ← إعدادات النظام ← إعدادات الخادم" -ForegroundColor White
Write-Host "   • تأكد من اتصال جميع الأجهزة بنفس الشبكة" -ForegroundColor White
Write-Host "   • يمكنك تغيير إعدادات الخادم من واجهة النظام دون إعادة تشغيل" -ForegroundColor White
Write-Host ""
Write-Host "================================================================================" -ForegroundColor Cyan

# فتح المتصفح إذا طُلب ذلك
if ($OpenBrowser) {
    Write-Host "🌐 فتح المتصفح..." -ForegroundColor Blue
    Start-Process "${protocol}://localhost:$($serverConfig.port)"
}

# بدء تشغيل الخادم
Write-Host "🚀 جاري تشغيل خادم النظام..." -ForegroundColor Green
Write-Host ""
Write-Host "⚠️  لإيقاف الخادم اضغط Ctrl+C" -ForegroundColor Yellow
Write-Host "🔧 لتغيير إعدادات الخادم: سجل دخول كمدير ← إعدادات النظام ← إعدادات الخادم" -ForegroundColor Blue
Write-Host ""

# تشغيل التطبيق
try {
    python app.py
} catch {
    Write-Host ""
    Write-Host "❌ حدث خطأ في تشغيل التطبيق" -ForegroundColor Red
    Write-Host "تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🛑 تم إيقاف الخادم" -ForegroundColor Red
Write-Host "📝 لمراجعة دليل الإعداد، راجع ملف README_SERVER_SETUP.md" -ForegroundColor Blue
Read-Host "اضغط Enter للخروج"
