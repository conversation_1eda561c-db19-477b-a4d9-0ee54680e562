{% extends "base.html" %}

{% block title %}القضايا - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder-open me-2"></i>
        إدارة القضايا
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ {% url 'cases_add' %} }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة قضية جديدة
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="رقم القضية أو العنوان">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>معلقة</option>
                    <option value="active" {% if status == 'active' %}selected{% endif %}>نشطة</option>
                    <option value="completed" {% if status == 'completed' %}selected{% endif %}>مكتملة</option>
                    <option value="won" {% if status == 'won' %}selected{% endif %}>مكسوبة</option>
                    <option value="lost" {% if status == 'lost' %}selected{% endif %}>مخسورة</option>
                    <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغية</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ {% url 'cases_index' %} }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Cases Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة القضايا</h5>
    </div>
    <div class="card-body">
        {% if cases.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم القضية</th>
                        <th>عنوان القضية</th>
                        <th>العميل</th>
                        <th>المحامي</th>
                        <th>نوع القضية</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in cases.items %}
                    <tr>
                        <td>
                            <strong>{{ case.case_number }}</strong>
                        </td>
                        <td>
                            <a href="{{ url_for('cases.view', id=case.id) }}" class="text-decoration-none">
                                {{ case.case_title }}
                            </a>
                        </td>
                        <td>{{ case.client.full_name }}</td>
                        <td>{{ case.lawyer.user.full_name }}</td>
                        <td>{{ case.case_type }}</td>
                        <td>
                            {% if case.case_status == 'pending' %}
                                <span class="badge status-pending">معلقة</span>
                            {% elif case.case_status == 'active' %}
                                <span class="badge status-active">نشطة</span>
                            {% elif case.case_status == 'completed' %}
                                <span class="badge status-completed">مكتملة</span>
                            {% elif case.case_status == 'won' %}
                                <span class="badge bg-success">مكسوبة</span>
                            {% elif case.case_status == 'lost' %}
                                <span class="badge bg-danger">مخسورة</span>
                            {% else %}
                                <span class="badge status-cancelled">ملغية</span>
                            {% endif %}
                        </td>
                        <td>{{ case.created_at|strftime("%Y-%m-%d") }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('cases.view', id=case.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('cases.edit', id=case.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="confirmDelete('{{ case.id }}')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if cases.pages > 1 %}
        <nav aria-label="صفحات القضايا">
            <ul class="pagination justify-content-center">
                {% if cases.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('cases.index', page=cases.prev_num, search=search, status=status) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in cases.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != cases.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('cases.index', page=page_num, search=search, status=status) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if cases.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('cases.index', page=cases.next_num, search=search, status=status) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد قضايا</h5>
            <p class="text-muted">لم يتم العثور على أي قضايا تطابق معايير البحث.</p>
            <a href="{{ {% url 'cases_add' %} }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة قضية جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف هذه القضية؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(caseId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/cases/${caseId}/delete`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}
