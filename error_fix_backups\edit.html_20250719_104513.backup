{% extends "base.html" %}

{% block title %}تعديل العميل: {{ client.full_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>
        تعديل العميل: {{ client.full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('clients.view', id=client.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لتفاصيل العميل
            </a>
            <a href="{{ url_for('clients.index') }}" class="btn btn-outline-info">
                <i class="fas fa-list me-1"></i>
                قائمة العملاء
            </a>
        </div>
    </div>
</div>

<form method="POST" id="editClientForm">
    <div class="row">
        <div class="col-md-8">
            <!-- معلومات العميل الأساسية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        معلومات العميل الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="{{ client.full_name }}" required>
                                <div class="form-text">أدخل الاسم الكامل للعميل أو اسم الشركة</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="client_type" class="form-label">نوع العميل <span class="text-danger">*</span></label>
                                <select class="form-select" id="client_type" name="client_type" required>
                                    <option value="individual" {{ 'selected' if client.client_type == 'individual' }}>فرد</option>
                                    <option value="company" {{ 'selected' if client.client_type == 'company' }}>شركة</option>
                                    <option value="organization" {{ 'selected' if client.client_type == 'organization' }}>مؤسسة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ client.email or '' }}">
                                <div class="form-text">سيتم استخدامه للتواصل وإرسال الإشعارات</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ client.phone or '' }}" placeholder="+966501234567">
                                <div class="form-text">يفضل تضمين رمز الدولة</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="national_id" class="form-label">رقم الهوية/السجل التجاري</label>
                                <input type="text" class="form-control" id="national_id" name="national_id" 
                                       value="{{ client.national_id or '' }}">
                                <div class="form-text">رقم الهوية الوطنية أو السجل التجاري</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رمز العميل</label>
                                <input type="text" class="form-control" value="{{ client.client_code }}" readonly>
                                <div class="form-text">رمز العميل لا يمكن تغييره</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3" 
                                  placeholder="أدخل العنوان الكامل للعميل">{{ client.address or '' }}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات إضافية حول العميل">{{ client.notes or '' }}</textarea>
                    </div>

                    <!-- حالة العميل -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {{ 'checked' if client.is_active }}>
                            <label class="form-check-label" for="is_active">
                                العميل نشط
                            </label>
                            <div class="form-text">إلغاء التحديد سيؤدي إلى إلغاء تفعيل العميل</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- معلومات إضافية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-lightbulb me-1"></i>
                            نصائح مهمة:
                        </h6>
                        <ul class="mb-0">
                            <li>تأكد من صحة البريد الإلكتروني</li>
                            <li>رقم الهاتف مهم للتواصل السريع</li>
                            <li>رقم الهوية يجب أن يكون فريد</li>
                            <li>العنوان مفيد للمراسلات الرسمية</li>
                            <li>الملاحظات تساعد في التذكير</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            تنبيه:
                        </h6>
                        <p class="mb-0">
                            تعديل بيانات العميل سيؤثر على جميع القضايا والعقود المرتبطة به.
                        </p>
                    </div>

                    <!-- معلومات النظام -->
                    <div class="mt-3">
                        <h6>معلومات النظام:</h6>
                        <small class="text-muted">
                            <p><strong>تاريخ الإنشاء:</strong><br>{{ client.created_at.strftime('%Y-%m-%d %H:%M') if client.created_at else 'غير محدد' }}</p>
                            {% if client.updated_at %}
                            <p><strong>آخر تحديث:</strong><br>{{ client.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                        <a href="{{ url_for('clients.view', id=client.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <hr>
                        <button type="button" onclick="resetForm()" class="btn btn-outline-warning">
                            <i class="fas fa-undo me-1"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>

            <!-- إجراءات متقدمة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إجراءات متقدمة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if client.is_active %}
                        <button type="button" onclick="deactivateClient()" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-user-times me-1"></i>
                            إلغاء تفعيل العميل
                        </button>
                        {% else %}
                        <button type="button" onclick="activateClient()" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-user-check me-1"></i>
                            تفعيل العميل
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
// التحقق من صحة النموذج
document.getElementById('editClientForm').addEventListener('submit', function(e) {
    const fullName = document.getElementById('full_name').value.trim();
    
    if (!fullName) {
        e.preventDefault();
        alert('يرجى إدخال اسم العميل');
        document.getElementById('full_name').focus();
        return false;
    }
    
    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    const email = document.getElementById('email').value.trim();
    if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('يرجى إدخال بريد إلكتروني صحيح');
            document.getElementById('email').focus();
            return false;
        }
    }
    
    // التحقق من رقم الهاتف إذا تم إدخاله
    const phone = document.getElementById('phone').value.trim();
    if (phone) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(phone)) {
            e.preventDefault();
            alert('يرجى إدخال رقم هاتف صحيح');
            document.getElementById('phone').focus();
            return false;
        }
    }
});

// تحديث النصوص حسب نوع العميل
document.getElementById('client_type').addEventListener('change', function() {
    const clientType = this.value;
    const nationalIdLabel = document.querySelector('label[for="national_id"]');
    const fullNameLabel = document.querySelector('label[for="full_name"]');
    
    if (clientType === 'individual') {
        nationalIdLabel.textContent = 'رقم الهوية الوطنية';
        fullNameLabel.textContent = 'الاسم الكامل';
    } else if (clientType === 'company') {
        nationalIdLabel.textContent = 'رقم السجل التجاري';
        fullNameLabel.textContent = 'اسم الشركة';
    } else if (clientType === 'organization') {
        nationalIdLabel.textContent = 'رقم التسجيل';
        fullNameLabel.textContent = 'اسم المؤسسة';
    }
});

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
        document.getElementById('editClientForm').reset();
        // إعادة تحميل الصفحة لاستعادة القيم الأصلية
        location.reload();
    }
}

// إلغاء تفعيل العميل
function deactivateClient() {
    if (confirm('هل أنت متأكد من إلغاء تفعيل هذا العميل؟\nسيتم إخفاؤه من القوائم النشطة.')) {
        document.getElementById('is_active').checked = false;
        document.getElementById('editClientForm').submit();
    }
}

// تفعيل العميل
function activateClient() {
    if (confirm('هل أنت متأكد من تفعيل هذا العميل؟')) {
        document.getElementById('is_active').checked = true;
        document.getElementById('editClientForm').submit();
    }
}

// تشغيل تحديث النصوص عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('client_type').dispatchEvent(new Event('change'));
});
</script>
{% endblock %}
