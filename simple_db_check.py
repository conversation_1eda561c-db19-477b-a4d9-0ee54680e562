import sqlite3
import os

print("Starting database check...")

databases = ['legal_system.db', 'instance/legal_system.db']

for db_path in databases:
    if os.path.exists(db_path):
        print(f"Checking {db_path}")
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"Tables: {tables}")
            conn.close()
        except Exception as e:
            print(f"Error: {e}")
    else:
        print(f"Database not found: {db_path}")

print("Database check completed.")
