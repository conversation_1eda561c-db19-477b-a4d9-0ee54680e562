"""
ملف مساعد للوصول إلى النماذج من المسارات
"""

def get_models():
    """الحصول على جميع النماذج وقاعدة البيانات"""
    try:
        from app import (
            User, Client, Lawyer, Case, CaseSession, 
            Appointment, Document, Invoice, Payment, 
            CaseNote, CaseTimeline, db
        )
        return {
            'User': User,
            'Client': Client,
            'Lawyer': Lawyer,
            'Case': Case,
            'CaseSession': CaseSession,
            'Appointment': Appointment,
            'Document': Document,
            'Invoice': Invoice,
            'Payment': Payment,
            'CaseNote': CaseNote,
            'CaseTimeline': CaseTimeline,
            'db': db
        }
    except ImportError as e:
        print(f"خطأ في استيراد النماذج: {e}")
        return None

def get_db():
    """الحصول على قاعدة البيانات فقط"""
    try:
        from app import db
        return db
    except ImportError:
        return None
