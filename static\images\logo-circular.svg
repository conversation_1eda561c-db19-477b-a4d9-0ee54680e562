<svg width="80" height="80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007bff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0056b3;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
  </defs>
  
  <!-- الخلفية الدائرية البيضاء -->
  <circle cx="40" cy="40" r="38" fill="url(#bgGrad)" stroke="#e9ecef" stroke-width="2" filter="url(#shadow)"/>
  
  <!-- دائرة داخلية للأيقونة -->
  <circle cx="40" cy="40" r="28" fill="none" stroke="url(#iconGrad)" stroke-width="2" opacity="0.3"/>
  
  <!-- أيقونة العدالة -->
  <g transform="translate(40, 40)">
    <!-- الميزان الرئيسي -->
    <line x1="0" y1="-15" x2="0" y2="15" stroke="url(#iconGrad)" stroke-width="3" stroke-linecap="round"/>
    
    <!-- العارضة الأفقية -->
    <line x1="-12" y1="-8" x2="12" y2="-8" stroke="url(#iconGrad)" stroke-width="2.5" stroke-linecap="round"/>
    
    <!-- الكفة اليسرى -->
    <path d="M-12 -8 Q-15 -5 -12 -2 Q-9 -5 -12 -8" fill="none" stroke="url(#iconGrad)" stroke-width="2"/>
    <line x1="-12" y1="-8" x2="-12" y2="-2" stroke="url(#iconGrad)" stroke-width="1"/>
    
    <!-- الكفة اليمنى -->
    <path d="M12 -8 Q15 -5 12 -2 Q9 -5 12 -8" fill="none" stroke="url(#iconGrad)" stroke-width="2"/>
    <line x1="12" y1="-8" x2="12" y2="-2" stroke="url(#iconGrad)" stroke-width="1"/>
    
    <!-- القاعدة -->
    <ellipse cx="0" cy="15" rx="8" ry="2" fill="url(#iconGrad)" opacity="0.6"/>
    
    <!-- نقطة التوازن -->
    <circle cx="0" cy="-8" r="2" fill="url(#iconGrad)"/>
    
    <!-- زخرفة إضافية -->
    <circle cx="0" cy="0" r="1" fill="url(#iconGrad)" opacity="0.8"/>
  </g>
  
  <!-- نص صغير (اختياري) -->
  <text x="40" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="url(#iconGrad)" opacity="0.7">
    قانوني
  </text>
</svg>
