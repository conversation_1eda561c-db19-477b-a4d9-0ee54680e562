#!/usr/bin/env python3
"""
ملف تشغيل نظام الشؤون القانونية
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def check_requirements():
    """التحقق من المتطلبات"""
    required_packages = [
        ('flask', 'Flask'),
        ('flask_sqlalchemy', 'Flask-SQLAlchemy'),
        ('flask_login', 'Flask-Login'),
        ('werkzeug', 'Werkzeug')
    ]

    missing_packages = []

    for package_name, display_name in required_packages:
        try:
            __import__(package_name)
            print(f"✅ {display_name} متوفر")
        except ImportError:
            missing_packages.append(display_name)
            print(f"❌ {display_name} مفقود")

    if missing_packages:
        print(f"\n❌ المكتبات المفقودة: {', '.join(missing_packages)}")
        print("يرجى تشغيل: pip install -r requirements.txt")
        return False

    print("✅ جميع المتطلبات متوفرة")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام الشؤون القانونية...")
    print("="*50)
    
    # التحقق من إصدار Python
    if not check_python_version():
        return
    
    # التحقق من المتطلبات
    if not check_requirements():
        response = input("هل تريد تثبيت المتطلبات تلقائياً؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم']:
            if not install_requirements():
                return
        else:
            return
    
    # تشغيل التطبيق
    try:
        print("🌐 تشغيل الخادم...")
        import app
        # التطبيق سيعمل من خلال app.py
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
