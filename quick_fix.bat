@echo off
chcp 65001 >nul
title إصلاح Flask - نظام الشؤون القانونية

echo.
echo ========================================
echo    إصلاح Flask - نظام الشؤون القانونية
echo ========================================
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python 3.8 أو أحدث من python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM تحديث pip
echo 📦 تحديث pip...
python -m pip install --upgrade pip >nul 2>&1

REM مسح cache
echo 🧹 مسح cache...
python -m pip cache purge >nul 2>&1

REM تثبيت الحزم الأساسية
echo 🔧 تثبيت الحزم الأساسية...
echo    - Flask...
python -m pip install Flask>=2.3.0 --upgrade >nul 2>&1

echo    - Flask-SQLAlchemy...
python -m pip install Flask-SQLAlchemy>=3.0.0 --upgrade >nul 2>&1

echo    - Flask-Login...
python -m pip install Flask-Login>=0.6.0 --upgrade >nul 2>&1

echo    - Werkzeug...
python -m pip install Werkzeug>=2.3.0 --upgrade >nul 2>&1

echo    - python-dotenv...
python -m pip install python-dotenv --upgrade >nul 2>&1

REM تثبيت الحزم الاختيارية
echo 🔧 تثبيت الحزم الاختيارية...
python -m pip install Flask-WTF WTForms bcrypt python-dateutil email-validator --upgrade >nul 2>&1

REM اختبار Flask
echo 🧪 اختبار Flask...
python -c "import flask; print('✅ Flask', flask.__version__, 'يعمل بشكل صحيح')" 2>nul
if errorlevel 1 (
    echo ❌ Flask لا يعمل بشكل صحيح
    pause
    exit /b 1
)

REM اختبار التطبيق
echo 🧪 اختبار التطبيق...
python -c "import app; print('✅ التطبيق يعمل بشكل صحيح')" 2>nul
if errorlevel 1 (
    echo ⚠️  تحذير: قد تكون هناك مشاكل في التطبيق
) else (
    echo ✅ التطبيق يعمل بشكل صحيح
)

echo.
echo ========================================
echo 🎉 تم إصلاح Flask بنجاح!
echo ========================================
echo.
echo 🚀 يمكنك الآن تشغيل التطبيق:
echo    python app.py
echo.
echo أو استخدام:
echo    start_server.bat
echo.
echo ========================================

pause
