#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إصلاح Django URL syntax إلى Flask URL syntax
"""

import os
import re
import shutil
from datetime import datetime

def fix_url_syntax():
    """إصلاح Django URL syntax إلى Flask URL syntax"""
    
    print("🔧 إصلاح Django URL syntax إلى Flask...")
    
    # إنشاء مجلد النسخ الاحتياطية
    backup_dir = "url_fix_backups"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    fixed_files = 0
    total_fixes = 0
    
    # قاموس تحويل URLs من Django إلى Flask
    url_mappings = {
        'dashboard': 'dashboard',
        'login': 'login',
        'logout': 'logout',
        'index': 'index',
        'admin.index': 'admin_index',
        'admin.users': 'admin_users',
        'admin.add_user': 'admin_add_user',
        'admin.profile': 'admin_profile',
        'admin.edit_profile': 'admin_edit_profile',
        'admin.backup': 'admin_backup',
        'admin.settings': 'admin_settings',
        'admin.logs': 'admin_logs',
        'attendance.index': 'attendance_index',
        'attendance.add': 'attendance_add',
        'attendance.report': 'attendance_report',
        'leaves.index': 'leaves_index',
        'leaves.add': 'leaves_add',
        'leaves.report': 'leaves_report',
        'warnings.index': 'warnings_index',
        'warnings.add': 'warnings_add',
        'warnings.report': 'warnings_report',
        'penalties.index': 'penalties_index',
        'penalties.add': 'penalties_add',
        'penalties.report': 'penalties_report',
        'clients.index': 'clients_index',
        'clients.add': 'clients_add',
        'cases.index': 'cases_index',
        'cases.add': 'cases_add',
        'employees.index': 'employees_index',
        'employees.add': 'employees_add',
        'employees.salary_report': 'employees_salary_report',
        'contracts.index': 'contracts_index',
        'contracts.add': 'contracts_add',
        'contracts.reports': 'contracts_reports',
        'settings.index': 'settings_index',
        'settings.company': 'settings_company',
        'settings.reports': 'settings_reports',
        'appointments.add': 'appointments_add',
        'invoices.add': 'invoices_add',
    }
    
    # البحث في جميع ملفات HTML
    for root, dirs, files in os.walk("templates"):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    file_fixes = 0
                    
                    # إصلاح {{ {% url 'view_name' %} }} إلى {{ url_for('view_name') }}
                    for django_url, flask_url in url_mappings.items():
                        pattern = rf'\{{\{{\s*\{{\%\s*url\s+[\'"]({re.escape(django_url)})[\'"].*?\%\}}\s*\}}\}}'
                        replacement = f"{{{{ url_for('{flask_url}') }}}}"
                        
                        matches = re.findall(pattern, content)
                        if matches:
                            content = re.sub(pattern, replacement, content)
                            file_fixes += len(matches)
                    
                    # إصلاح الأنماط الأخرى
                    patterns = [
                        (r'\{{\s*\{\%\s*url\s+[\'"]([^\'\"]+)[\'"].*?\%\}\s*\}\}', r"{{ url_for('\1') }}"),
                        (r'href="\{{\s*\{\%\s*url\s+[\'"]([^\'\"]+)[\'"].*?\%\}\s*\}\}"', r'href="{{ url_for(\'\1\') }}"'),
                        (r'action="\{{\s*\{\%\s*url\s+[\'"]([^\'\"]+)[\'"].*?\%\}\s*\}\}"', r'action="{{ url_for(\'\1\') }}"'),
                    ]
                    
                    for pattern, replacement in patterns:
                        matches = re.findall(pattern, content)
                        if matches:
                            content = re.sub(pattern, replacement, content)
                            file_fixes += len(matches)
                    
                    if content != original_content:
                        # إنشاء نسخة احتياطية
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        backup_file = os.path.join(backup_dir, f"{file}_{timestamp}.backup")
                        shutil.copy2(file_path, backup_file)
                        
                        # حفظ الملف المُصلح
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        total_fixes += file_fixes
                        fixed_files += 1
                        
                        print(f"✅ تم إصلاح {file_path} - {file_fixes} إصلاح")
                
                except Exception as e:
                    print(f"❌ خطأ في معالجة {file_path}: {e}")
    
    print(f"\n🎉 تم الانتهاء!")
    print(f"📁 الملفات المُصلحة: {fixed_files}")
    print(f"🔧 إجمالي الإصلاحات: {total_fixes}")
    print(f"💾 النسخ الاحتياطية في: {backup_dir}")

if __name__ == "__main__":
    fix_url_syntax()
