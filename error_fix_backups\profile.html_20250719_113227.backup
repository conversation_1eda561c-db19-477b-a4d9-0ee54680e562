{% extends "base.html" %} {% block title %}الملف الشخصي - نظام الشؤون
القانونية{% endblock %} {% block extra_css %}
<style>
  .profile-image {
    background: white;
    border-radius: 50%;
    padding: 5px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    border: 3px solid #007bff;
    transition: all 0.3s ease;
  }

  .profile-image:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
  }

  .logo-in-profile {
    background: white;
    border-radius: 50%;
    padding: 3px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 2px solid #e9ecef;
  }
</style>
{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2">
    <i class="fas fa-user me-2"></i>
    الملف الشخصي
  </h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
      <a href="{{ {% url 'admin.edit_profile' %} }}" class="btn btn-primary">
        <i class="fas fa-edit me-1"></i>
        تعديل الملف الشخصي
      </a>
      <button
        type="button"
        class="btn btn-outline-warning"
        data-bs-toggle="modal"
        data-bs-target="#changePasswordModal"
      >
        <i class="fas fa-key me-1"></i>
        تغيير كلمة المرور
      </button>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-4">
    <!-- بطاقة الملف الشخصي -->
    <div class="card">
      <div class="card-body text-center">
        <div class="mb-3">
          <img
            src="https://ui-avatars.com/api/?name={{ current_user.full_name }}&background=007bff&color=fff&size=120"
            class="rounded-circle mb-3"
            alt="صورة الملف الشخصي"
            width="120"
            height="120"
          />
        </div>
        <h4 class="card-title">{{ current_user.full_name }}</h4>
        <p class="text-muted">{{ current_user.username }}@</p>

        {% if current_user.role == 'admin' %}
        <span class="badge bg-danger mb-3">مدير النظام</span>
        {% elif current_user.role == 'lawyer' %}
        <span class="badge bg-primary mb-3">محامي</span>
        {% elif current_user.role == 'secretary' %}
        <span class="badge bg-info mb-3">سكرتير</span>
        {% else %}
        <span class="badge bg-secondary mb-3">{{ current_user.role }}</span>
        {% endif %}

        <div class="d-grid gap-2">
          <button
            type="button"
            class="btn btn-outline-primary btn-sm"
            onclick="uploadPhoto()"
          >
            <i class="fas fa-camera me-1"></i>
            تغيير الصورة
          </button>
        </div>
      </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="card mt-3">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fas fa-chart-bar me-2"></i>
          إحصائياتي
        </h6>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-6 mb-3">
            <div class="border rounded p-2">
              <h4 class="text-primary mb-1">15</h4>
              <small class="text-muted">القضايا النشطة</small>
            </div>
          </div>
          <div class="col-6 mb-3">
            <div class="border rounded p-2">
              <h4 class="text-success mb-1">8</h4>
              <small class="text-muted">المهام المكتملة</small>
            </div>
          </div>
          <div class="col-6">
            <div class="border rounded p-2">
              <h4 class="text-warning mb-1">3</h4>
              <small class="text-muted">المواعيد القادمة</small>
            </div>
          </div>
          <div class="col-6">
            <div class="border rounded p-2">
              <h4 class="text-info mb-1">25</h4>
              <small class="text-muted">العملاء</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-lg-8">
    <!-- معلومات الملف الشخصي -->
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-info-circle me-2"></i>
          المعلومات الشخصية
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label text-muted">الاسم الكامل</label>
            <p class="fw-bold">{{ current_user.full_name }}</p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label text-muted">اسم المستخدم</label>
            <p class="fw-bold">{{ current_user.username }}</p>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label text-muted">البريد الإلكتروني</label>
            <p class="fw-bold">{{ current_user.email or 'غير محدد' }}</p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label text-muted">رقم الهاتف</label>
            <p class="fw-bold">{{ current_user.phone or 'غير محدد' }}</p>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label text-muted">الدور</label>
            <p class="fw-bold">
              {% if current_user.role == 'admin' %} مدير النظام {% elif
              current_user.role == 'lawyer' %} محامي {% elif current_user.role
              == 'secretary' %} سكرتير {% else %} {{ current_user.role }} {%
              endif %}
            </p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label text-muted">تاريخ التسجيل</label>
            <p class="fw-bold">
              {{ current_user.created_at|date:"%Y-%m-%d" if
              current_user.created_at else 'غير محدد' }}
            </p>
          </div>
        </div>

        {% if current_user.address %}
        <div class="mb-3">
          <label class="form-label text-muted">العنوان</label>
          <p class="fw-bold">{{ current_user.address }}</p>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- نشاط الحساب -->
    <div class="card mt-3">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-history me-2"></i>
          نشاط الحساب
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label text-muted">آخر تسجيل دخول</label>
            <p class="fw-bold">
              {{ current_user.last_login|date:"%Y-%m-%d %H:%M" if
              current_user.last_login else 'الآن' }}
            </p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label text-muted">عدد مرات الدخول</label>
            <p class="fw-bold">{{ current_user.login_count or '1' }}</p>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label text-muted">حالة الحساب</label>
            <p class="fw-bold">
              {% if current_user.is_active %}
              <span class="badge bg-success">نشط</span>
              {% else %}
              <span class="badge bg-warning">غير نشط</span>
              {% endif %}
            </p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label text-muted">آخر تحديث للملف</label>
            <p class="fw-bold">
              {{ current_user.updated_at|date:"%Y-%m-%d %H:%M" if
              current_user.updated_at else 'غير محدد' }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- الأنشطة الأخيرة -->
    <div class="card mt-3">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-clock me-2"></i>
          الأنشطة الأخيرة
        </h5>
      </div>
      <div class="card-body">
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-marker bg-primary"></div>
            <div class="timeline-content">
              <h6 class="timeline-title">تسجيل دخول للنظام</h6>
              <p class="timeline-description">تم تسجيل الدخول بنجاح</p>
              <small class="text-muted">منذ 5 دقائق</small>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-marker bg-success"></div>
            <div class="timeline-content">
              <h6 class="timeline-title">إضافة قضية جديدة</h6>
              <p class="timeline-description">
                تم إضافة قضية "قضية تجارية - شركة ABC"
              </p>
              <small class="text-muted">منذ ساعة</small>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-marker bg-info"></div>
            <div class="timeline-content">
              <h6 class="timeline-title">تحديث بيانات عميل</h6>
              <p class="timeline-description">
                تم تحديث بيانات العميل "أحمد محمد"
              </p>
              <small class="text-muted">منذ 3 ساعات</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- نافذة تغيير كلمة المرور -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تغيير كلمة المرور</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <form id="changePasswordForm">
          <div class="mb-3">
            <label for="current_password" class="form-label"
              >كلمة المرور الحالية</label
            >
            <input
              type="password"
              class="form-control"
              id="current_password"
              required
            />
          </div>
          <div class="mb-3">
            <label for="new_password" class="form-label"
              >كلمة المرور الجديدة</label
            >
            <input
              type="password"
              class="form-control"
              id="new_password"
              required
            />
            <div class="form-text">يجب أن تكون 8 أحرف على الأقل</div>
          </div>
          <div class="mb-3">
            <label for="confirm_new_password" class="form-label"
              >تأكيد كلمة المرور الجديدة</label
            >
            <input
              type="password"
              class="form-control"
              id="confirm_new_password"
              required
            />
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          إلغاء
        </button>
        <button
          type="button"
          class="btn btn-primary"
          onclick="changePassword()"
        >
          تغيير كلمة المرور
        </button>
      </div>
    </div>
  </div>
</div>

<!-- نافذة رفع الصورة -->
<input type="file" id="photoUpload" accept="image/*" style="display: none" />
{% endblock %} {% block extra_css %}
<style>
  .timeline {
    position: relative;
    padding-left: 30px;
  }

  .timeline-item {
    position: relative;
    margin-bottom: 20px;
  }

  .timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .timeline-item:not(:last-child)::before {
    content: "";
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 20px);
    background-color: #dee2e6;
  }

  .timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
  }

  .timeline-title {
    margin-bottom: 5px;
    color: #495057;
  }

  .timeline-description {
    margin-bottom: 10px;
    color: #6c757d;
  }
</style>
{% endblock %} {% block extra_js %}
<script>
  function uploadPhoto() {
    document.getElementById("photoUpload").click();
  }

  document
    .getElementById("photoUpload")
    .addEventListener("change", function (e) {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
          // تحديث الصورة في الواجهة
          document.querySelector(".rounded-circle").src = e.target.result;
          alert("تم رفع الصورة بنجاح (وظيفة تجريبية)");
        };
        reader.readAsDataURL(file);
      }
    });

  function changePassword() {
    const currentPassword = document.getElementById("current_password").value;
    const newPassword = document.getElementById("new_password").value;
    const confirmPassword = document.getElementById(
      "confirm_new_password"
    ).value;

    if (!currentPassword || !newPassword || !confirmPassword) {
      alert("يرجى ملء جميع الحقول");
      return;
    }

    if (newPassword !== == confirmPassword) {
      alert("كلمات المرور الجديدة غير متطابقة");
      return;
    }

    if (newPassword.length < 8) {
      alert("كلمة المرور يجب أن تكون 8 أحرف على الأقل");
      return;
    }

    // محاكاة تغيير كلمة المرور
    alert("تم تغيير كلمة المرور بنجاح (وظيفة تجريبية)");

    // إغلاق النافذة وإعادة تعيين النموذج
    const modal = bootstrap.Modal.getInstance(
      document.getElementById("changePasswordModal")
    );
    modal.hide();
    document.getElementById("changePasswordForm").reset();
  }
</script>
{% endblock %}
