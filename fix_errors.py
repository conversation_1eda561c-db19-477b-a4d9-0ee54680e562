#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت سريع لإصلاح الأخطاء
Quick Error Fix Script

سكريبت مبسط لإصلاح الأخطاء الشائعة بسرعة
"""

import sys
import argparse
from pathlib import Path
from auto_error_fixer import AutoErrorFixer

def quick_template_fix():
    """إصلاح سريع لملفات القوالب فقط"""
    print("🔧 إصلاح سريع لملفات القوالب...")
    
    fixer = AutoErrorFixer()
    templates_dir = Path("templates")
    
    if not templates_dir.exists():
        print("❌ مجلد القوالب غير موجود")
        return False
    
    fixed_count = 0
    for template_file in templates_dir.rglob("*.html"):
        if fixer.fix_template_file(template_file):
            fixed_count += 1
            fixer.add_javascript_for_dynamic_styles(template_file)
    
    print(f"✅ تم إصلاح {fixed_count} ملف قالب")
    return True

def quick_python_fix():
    """إصلاح سريع لملفات Python فقط"""
    print("🔧 إصلاح سريع لملفات Python...")
    
    fixer = AutoErrorFixer()
    fixed_count = 0
    
    for py_file in Path(".").rglob("*.py"):
        if "venv" not in str(py_file) and "__pycache__" not in str(py_file):
            if fixer.fix_python_file(py_file):
                fixed_count += 1
    
    print(f"✅ تم إصلاح {fixed_count} ملف Python")
    return True

def quick_js_fix():
    """إصلاح سريع لملفات JavaScript فقط"""
    print("🔧 إصلاح سريع لملفات JavaScript...")
    
    fixer = AutoErrorFixer()
    fixed_count = 0
    
    # ملفات JS في مجلد static
    static_dir = Path("static")
    if static_dir.exists():
        for js_file in static_dir.rglob("*.js"):
            if fixer.fix_javascript_file(js_file):
                fixed_count += 1
    
    # JavaScript مضمن في القوالب
    templates_dir = Path("templates")
    if templates_dir.exists():
        for template_file in templates_dir.rglob("*.html"):
            if fixer.fix_inline_javascript(template_file):
                fixed_count += 1
    
    print(f"✅ تم إصلاح {fixed_count} ملف JavaScript")
    return True

def fix_specific_file(file_path: str):
    """إصلاح ملف محدد"""
    file_path = Path(file_path)
    
    if not file_path.exists():
        print(f"❌ الملف غير موجود: {file_path}")
        return False
    
    print(f"🔧 إصلاح الملف: {file_path}")
    
    fixer = AutoErrorFixer()
    
    if file_path.suffix == '.html':
        success = fixer.fix_template_file(file_path)
        if success:
            fixer.add_javascript_for_dynamic_styles(file_path)
    elif file_path.suffix == '.py':
        success = fixer.fix_python_file(file_path)
    elif file_path.suffix == '.js':
        success = fixer.fix_javascript_file(file_path)
    else:
        print(f"❌ نوع ملف غير مدعوم: {file_path.suffix}")
        return False
    
    if success:
        print(f"✅ تم إصلاح الملف بنجاح")
    else:
        print(f"ℹ️ لا توجد أخطاء في الملف أو لم يتم العثور على أخطاء قابلة للإصلاح")
    
    return success

def emergency_fix():
    """إصلاح طوارئ - إصلاح الأخطاء الحرجة فقط"""
    print("🚨 وضع الطوارئ - إصلاح الأخطاء الحرجة...")
    
    fixer = AutoErrorFixer()
    
    # إصلاحات طوارئ للقوالب
    critical_template_fixes = {
        r'url_for\([\'"]([^\'\"]+)[\'\"]\)': r"{% url '\1' %}",
        r'\{\%\s*break\s*\%\}': '',
        r'style="[^"]*\{\%[^"]*\%\}[^"]*"': 'data-dynamic-style="true"',
    }
    
    # تطبيق الإصلاحات الحرجة فقط
    original_fixes = fixer.template_fixes
    fixer.template_fixes = critical_template_fixes
    
    results = fixer.scan_and_fix_all()
    
    # استعادة الإصلاحات الأصلية
    fixer.template_fixes = original_fixes
    
    print(f"✅ تم إصلاح {results['total_errors_fixed']} خطأ حرج")
    return True

def main():
    """الوظيفة الرئيسية"""
    parser = argparse.ArgumentParser(
        description="سكريبت إصلاح الأخطاء السريع",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python fix_errors.py --templates     # إصلاح القوالب فقط
  python fix_errors.py --python        # إصلاح ملفات Python فقط
  python fix_errors.py --javascript    # إصلاح ملفات JavaScript فقط
  python fix_errors.py --file path.html # إصلاح ملف محدد
  python fix_errors.py --emergency     # إصلاح طوارئ
  python fix_errors.py --all           # إصلاح شامل (افتراضي)
        """
    )
    
    parser.add_argument('--templates', action='store_true', 
                       help='إصلاح ملفات القوالب فقط')
    parser.add_argument('--python', action='store_true', 
                       help='إصلاح ملفات Python فقط')
    parser.add_argument('--javascript', action='store_true', 
                       help='إصلاح ملفات JavaScript فقط')
    parser.add_argument('--file', type=str, 
                       help='إصلاح ملف محدد')
    parser.add_argument('--emergency', action='store_true', 
                       help='إصلاح طوارئ للأخطاء الحرجة فقط')
    parser.add_argument('--all', action='store_true', 
                       help='إصلاح شامل لجميع الملفات')
    
    args = parser.parse_args()
    
    print("🔧 سكريبت إصلاح الأخطاء السريع")
    print("=" * 40)
    
    try:
        if args.templates:
            quick_template_fix()
        elif args.python:
            quick_python_fix()
        elif args.javascript:
            quick_js_fix()
        elif args.file:
            fix_specific_file(args.file)
        elif args.emergency:
            emergency_fix()
        elif args.all or len(sys.argv) == 1:
            # إصلاح شامل (افتراضي)
            print("🔧 إصلاح شامل لجميع الملفات...")
            from auto_error_fixer import main as full_fix
            full_fix()
        else:
            parser.print_help()
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية")
    except Exception as e:
        print(f"❌ حدث خطأ: {e}")

if __name__ == "__main__":
    main()
