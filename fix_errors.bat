@echo off
chcp 65001 >nul
title معالج الأخطاء التلقائي

echo.
echo ========================================
echo        معالج الأخطاء التلقائي
echo ========================================
echo.

:menu
echo اختر نوع الإصلاح:
echo.
echo 1. إصلاح شامل لجميع الملفات
echo 2. إصلاح القوالب فقط
echo 3. إصلاح ملفات Python فقط  
echo 4. إصلاح ملفات JavaScript فقط
echo 5. إصلاح طوارئ (الأخطاء الحرجة فقط)
echo 6. إصلاح ملف محدد
echo 7. خروج
echo.

set /p choice="اختر رقم (1-7): "

if "%choice%"=="1" goto full_fix
if "%choice%"=="2" goto templates_fix
if "%choice%"=="3" goto python_fix
if "%choice%"=="4" goto js_fix
if "%choice%"=="5" goto emergency_fix
if "%choice%"=="6" goto specific_file
if "%choice%"=="7" goto exit
goto invalid

:full_fix
echo.
echo تشغيل الإصلاح الشامل...
python auto_error_fixer.py
goto end

:templates_fix
echo.
echo إصلاح ملفات القوالب...
python fix_errors.py --templates
goto end

:python_fix
echo.
echo إصلاح ملفات Python...
python fix_errors.py --python
goto end

:js_fix
echo.
echo إصلاح ملفات JavaScript...
python fix_errors.py --javascript
goto end

:emergency_fix
echo.
echo إصلاح طوارئ...
python fix_errors.py --emergency
goto end

:specific_file
echo.
set /p filepath="أدخل مسار الملف: "
python fix_errors.py --file "%filepath%"
goto end

:invalid
echo.
echo اختيار غير صحيح! حاول مرة أخرى.
echo.
goto menu

:end
echo.
echo ========================================
echo تم الانتهاء من العملية
echo ========================================
echo.
set /p continue="هل تريد تشغيل عملية أخرى؟ (y/n): "
if /i "%continue%"=="y" goto menu
if /i "%continue%"=="yes" goto menu

:exit
echo.
echo شكراً لاستخدام معالج الأخطاء التلقائي!
pause
