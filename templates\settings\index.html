{% extends "base.html" %}

{% block title %}إعدادات النظام - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cogs me-2"></i>
        إعدادات النظام
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ {% url 'dashboard' %} }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>

<!-- بطاقات الإعدادات الرئيسية -->
<div class="row mb-4">
    <div class="col-md-6 col-lg-4">
        <div class="card h-100 border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    إعدادات الشركة
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إدارة معلومات الشركة، الشعار، والبيانات التي تظهر في التقارير</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-1"></i> اسم الشركة ووصفها</li>
                    <li><i class="fas fa-check text-success me-1"></i> شعار الشركة</li>
                    <li><i class="fas fa-check text-success me-1"></i> معلومات الاتصال</li>
                    <li><i class="fas fa-check text-success me-1"></i> العنوان والموقع</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="{{ {% url 'settings_company' %} }}" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i>
                    تعديل إعدادات الشركة
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-4">
        <div class="card h-100 border-info">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    إعدادات التقارير
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">تخصيص شكل ومحتوى التقارير المطبوعة</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-1"></i> إظهار/إخفاء الشعار</li>
                    <li><i class="fas fa-check text-success me-1"></i> إظهار وقت الطباعة</li>
                    <li><i class="fas fa-check text-success me-1"></i> التوقيعات</li>
                    <li><i class="fas fa-check text-success me-1"></i> نص الذيل</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="{{ {% url 'settings_reports' %} }}" class="btn btn-info">
                    <i class="fas fa-edit me-1"></i>
                    تعديل إعدادات التقارير
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-4">
        <div class="card h-100 border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    إعدادات النظام
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إعدادات تقنية ومتقدمة للنظام</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-1"></i> إعدادات قاعدة البيانات</li>
                    <li><i class="fas fa-check text-success me-1"></i> النسخ الاحتياطية</li>
                    <li><i class="fas fa-check text-success me-1"></i> سجلات النظام</li>
                    <li><i class="fas fa-check text-success me-1"></i> الأمان والصلاحيات</li>
                </ul>
            </div>
            <div class="card-footer">
                <button class="btn btn-warning" onclick="alert('قريباً...')">
                    <i class="fas fa-edit me-1"></i>
                    إعدادات النظام
                </button>
            </div>
        </div>
    </div>
</div>

<!-- الإعدادات الحالية -->
{% if settings_by_category %}
<div class="row">
    {% for category, settings in settings_by_category %}
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if category == 'company' %}
                        <i class="fas fa-building me-2"></i>
                    {% elif category == 'reports' %}
                        <i class="fas fa-file-alt me-2"></i>
                    {% elif category == 'system' %}
                        <i class="fas fa-cogs me-2"></i>
                    {% else %}
                        <i class="fas fa-cog me-2"></i>
                    {% endif %}
                    {{ category_names.get(category, category) }}
                </h5>
            </div>
            <div class="card-body">
                {% if settings %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الإعداد</th>
                                <th>القيمة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for setting in settings|truncate(5) %}
                            <tr>
                                <td>
                                    <strong>{{ setting.display_name or setting.setting_key }}</strong>
                                    {% if setting.description %}
                                    <br><small class="text-muted">{{ setting.description }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if setting.setting_type == 'boolean' %}
                                        {% if setting.setting_value == 'true' %}
                                            <span class="badge bg-success">مفعل</span>
                                        {% else %}
                                            <span class="badge bg-secondary">معطل</span>
                                        {% endif %}
                                    {% elif setting.setting_type == 'file' %}
                                        <i class="fas fa-file me-1"></i>
                                        <small>{{ setting.setting_value.split('/')[-1] if setting.setting_value else 'لا يوجد' }}</small>
                                    {% else %}
                                        <small>{{ setting.setting_value|truncate(50) + '...' if setting.setting_value and setting.setting_value|length > 50 else (setting.setting_value or 'غير محدد') }}</small>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                            {% if settings|length > 5 %}
                            <tr>
                                <td colspan="2" class="text-center">
                                    <small class="text-muted">و {{ settings|length - 5 }} إعدادات أخرى...</small>
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد إعدادات في هذه الفئة</p>
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

<!-- إجراءات سريعة -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-bolt me-2"></i>
            إجراءات سريعة
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <div class="d-grid">
                    <button onclick="exportSettings()" class="btn btn-outline-primary">
                        <i class="fas fa-download me-1"></i>
                        تصدير الإعدادات
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-grid">
                    <button onclick="importSettings()" class="btn btn-outline-info">
                        <i class="fas fa-upload me-1"></i>
                        استيراد الإعدادات
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-grid">
                    <button onclick="backupSettings()" class="btn btn-outline-success">
                        <i class="fas fa-save me-1"></i>
                        نسخة احتياطية
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-grid">
                    <button onclick="resetAllSettings()" class="btn btn-outline-danger">
                        <i class="fas fa-undo me-1"></i>
                        إعادة تعيين الكل
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportSettings() {
    alert('ميزة تصدير الإعدادات قيد التطوير');
}

function importSettings() {
    alert('ميزة استيراد الإعدادات قيد التطوير');
}

function backupSettings() {
    alert('ميزة النسخ الاحتياطية قيد التطوير');
}

function resetAllSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!')) {
        if (confirm('تأكيد أخير: سيتم حذف جميع الإعدادات المخصصة. هل تريد المتابعة؟')) {
            fetch('/settings/reset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'category=all'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ في الاتصال بالخادم');
            });
        }
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type ===   'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid') || document.body;
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
