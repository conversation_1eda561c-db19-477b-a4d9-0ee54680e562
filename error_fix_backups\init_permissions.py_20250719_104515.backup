#!/usr/bin/env python3
"""
سكريبت تهيئة الصلاحيات والأدوار في قاعدة البيانات
"""

import sys
import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models import create_models
from permissions_manager import initialize_permissions

def create_app():
    """إنشاء تطبيق Flask للتهيئة"""
    app = Flask(__name__)
    
    # إعدادات قاعدة البيانات
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'legal_system.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'your-secret-key-here'
    
    return app

def init_database():
    """تهيئة قاعدة البيانات مع الصلاحيات"""
    print("🚀 بدء تهيئة قاعدة البيانات...")

    # إنشاء مجلد instance إذا لم يكن موجوداً
    os.makedirs('instance', exist_ok=True)

    app = create_app()
    
    with app.app_context():
        # إنشاء قاعدة البيانات
        db = SQLAlchemy(app)
        
        # إنشاء النماذج
        models = create_models(db)
        app.config['MODELS'] = models
        
        print("📊 إنشاء جداول قاعدة البيانات...")
        db.create_all()
        
        print("🔐 تهيئة الصلاحيات والأدوار...")
        initialize_permissions(db, models)
        
        # إنشاء مستخدم مدير افتراضي
        User = models['User']
        Role = models['Role']
        
        # البحث عن دور المدير
        admin_role = Role.query.filter_by(name='admin').first()
        
        # التحقق من وجود مستخدم مدير
        admin_user = User.query.filter_by(username='admin').first()
        
        if not admin_user:
            print("👤 إنشاء مستخدم مدير افتراضي...")
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                full_name='مدير النظام',
                role='admin',
                role_id=admin_role.id if admin_role else None,
                is_active=True
            )
            db.session.add(admin_user)
            db.session.commit()
            
            print("✅ تم إنشاء مستخدم المدير:")
            print(f"   اسم المستخدم: admin")
            print(f"   كلمة المرور: admin123")
            print(f"   البريد الإلكتروني: <EMAIL>")
        else:
            print("ℹ️  مستخدم المدير موجود بالفعل")
            # تحديث دور المدير إذا لم يكن محدد
            if not admin_user.role_id and admin_role:
                admin_user.role_id = admin_role.id
                admin_user.role = 'admin'
                db.session.commit()
                print("✅ تم تحديث دور المدير")
        
        # إنشاء بعض المستخدمين التجريبيين
        create_sample_users(db, models)

        # إنشاء بعض العملاء التجريبيين
        create_sample_clients(db, models)

        # إنشاء بعض العقود التجريبية
        create_sample_contracts(db, models)

        # إنشاء بيانات تجريبية للموظفين
        create_sample_employees(db, models)

        print("\n🎉 تم إكمال تهيئة قاعدة البيانات بنجاح!")
        print("\n📋 ملخص ما تم إنشاؤه:")
        
        # عرض إحصائيات
        Permission = models['Permission']
        total_permissions = Permission.query.count()
        total_roles = Role.query.count()
        total_users = User.query.count()
        
        print(f"   🔑 الصلاحيات: {total_permissions}")
        print(f"   👥 الأدوار: {total_roles}")
        print(f"   👤 المستخدمين: {total_users}")
        
        print("\n🌐 يمكنك الآن تشغيل النظام والدخول بالبيانات التالية:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")

def create_sample_users(db, models):
    """إنشاء مستخدمين تجريبيين"""
    User = models['User']
    Role = models['Role']
    
    # البحث عن الأدوار
    lawyer_role = Role.query.filter_by(name='lawyer').first()
    secretary_role = Role.query.filter_by(name='secretary').first()
    
    sample_users = [
        {
            'username': 'lawyer1',
            'email': '<EMAIL>',
            'full_name': 'د. سارة أحمد',
            'role': 'lawyer',
            'role_id': lawyer_role.id if lawyer_role else None
        },
        {
            'username': 'secretary1',
            'email': '<EMAIL>',
            'full_name': 'أحمد محمد',
            'role': 'secretary',
            'role_id': secretary_role.id if secretary_role else None
        },
        {
            'username': 'lawyer2',
            'email': '<EMAIL>',
            'full_name': 'د. محمد علي',
            'role': 'lawyer',
            'role_id': lawyer_role.id if lawyer_role else None
        }
    ]
    
    for user_data in sample_users:
        existing_user = User.query.filter_by(username=user_data['username']).first()
        if not existing_user:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                password_hash=generate_password_hash('123456'),
                full_name=user_data['full_name'],
                role=user_data['role'],
                role_id=user_data['role_id'],
                is_active=True
            )
            db.session.add(user)
    
    db.session.commit()
    print("✅ تم إنشاء المستخدمين التجريبيين")

def create_sample_clients(db, models):
    """إنشاء عملاء تجريبيين"""
    Client = models.get('Client')

    if not Client:
        print("⚠️  نموذج العملاء غير متاح")
        return

    from datetime import date
    import uuid

    sample_clients = [
        {
            'full_name': 'أحمد محمد السعيد',
            'client_type': 'individual',
            'email': '<EMAIL>',
            'phone': '+************',
            'national_id': '1234567890',
            'address': 'الرياض، حي النخيل، شارع الملك فهد، المملكة العربية السعودية',
            'notes': 'عميل مهم، يفضل التواصل عبر الهاتف'
        },
        {
            'full_name': 'شركة الأعمال المتقدمة المحدودة',
            'client_type': 'company',
            'email': '<EMAIL>',
            'phone': '+************',
            'national_id': '7001234567',
            'address': 'جدة، حي الحمراء، طريق الملك عبدالعزيز، المملكة العربية السعودية',
            'notes': 'شركة تجارية كبيرة، تتطلب خدمات قانونية شاملة'
        },
        {
            'full_name': 'فاطمة علي الزهراني',
            'client_type': 'individual',
            'email': '<EMAIL>',
            'phone': '+966509876543',
            'national_id': '9876543210',
            'address': 'الدمام، حي الشاطئ، شارع الخليج العربي، المملكة العربية السعودية',
            'notes': 'عميلة جديدة، تحتاج استشارة في قضايا الأحوال الشخصية'
        },
        {
            'full_name': 'مؤسسة التطوير العقاري',
            'client_type': 'organization',
            'email': '<EMAIL>',
            'phone': '+966113456789',
            'national_id': '8001234567',
            'address': 'الخبر، حي الراكة، شارع الأمير فيصل بن فهد، المملكة العربية السعودية',
            'notes': 'مؤسسة عقارية، تحتاج خدمات قانونية في مجال العقارات'
        },
        {
            'full_name': 'خالد عبدالله المطيري',
            'client_type': 'individual',
            'email': '<EMAIL>',
            'phone': '+966505555555',
            'national_id': '5555555555',
            'address': 'المدينة المنورة، حي العوالي، شارع سيد الشهداء، المملكة العربية السعودية',
            'notes': 'عميل دائم، يحتاج متابعة دورية لأعماله التجارية'
        },
        {
            'full_name': 'شركة التقنية الحديثة',
            'client_type': 'company',
            'email': '<EMAIL>',
            'phone': '+966114567890',
            'national_id': '9001234567',
            'address': 'الرياض، حي العليا، برج المملكة، المملكة العربية السعودية',
            'notes': 'شركة تقنية ناشئة، تحتاج استشارات في الملكية الفكرية'
        }
    ]

    for client_data in sample_clients:
        # التحقق من عدم وجود عميل بنفس الاسم
        existing_client = Client.query.filter_by(full_name=client_data['full_name']).first()
        if existing_client:
            continue

        # إنشاء رمز العميل
        client_code = f"CLIENT-{date.today().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

        client = Client(
            client_code=client_code,
            full_name=client_data['full_name'],
            client_type=client_data['client_type'],
            email=client_data['email'],
            phone=client_data['phone'],
            national_id=client_data['national_id'],
            address=client_data['address'],
            notes=client_data['notes'],
            is_active=True
        )

        db.session.add(client)

    db.session.commit()
    print("✅ تم إنشاء العملاء التجريبيين")

def create_sample_contracts(db, models):
    """إنشاء عقود تجريبية"""
    Contract = models['Contract']
    Client = models['Client']
    User = models['User']
    Lawyer = models['Lawyer']

    # التحقق من وجود عملاء ومحامين
    clients = Client.query.limit(3).all()
    lawyers = Lawyer.query.limit(2).all()
    admin_user = User.query.filter_by(username='admin').first()

    if not clients or not admin_user:
        print("⚠️  لا توجد بيانات كافية لإنشاء عقود تجريبية")
        return

    from datetime import date, timedelta
    from decimal import Decimal
    import uuid

    sample_contracts = [
        {
            'title': 'عقد استشارة قانونية شاملة',
            'title_en': 'Comprehensive Legal Consultation Contract',
            'contract_type': 'استشارة قانونية',
            'contract_type_en': 'Legal Consultation',
            'description': 'عقد تقديم استشارات قانونية شاملة في مجال القانون التجاري والمدني',
            'description_en': 'Contract for providing comprehensive legal consultations in commercial and civil law',
            'terms_and_conditions': '''1. يلتزم المحامي بتقديم الاستشارات القانونية المطلوبة
2. يحق للعميل الحصول على استشارات هاتفية مجانية
3. مدة الاستجابة للاستفسارات 24 ساعة كحد أقصى
4. يشمل العقد مراجعة الوثائق القانونية''',
            'payment_terms': 'الدفع على دفعتين: 50% عند التوقيع و50% بعد شهر',
            'start_date': date.today(),
            'end_date': date.today() + timedelta(days=365),
            'duration_months': 12,
            'total_amount': Decimal('15000.00'),
            'currency': 'SAR',
            'status': 'active',
            'priority': 'high'
        },
        {
            'title': 'عقد توكيل قضائي',
            'title_en': 'Legal Representation Contract',
            'contract_type': 'توكيل قضائي',
            'contract_type_en': 'Legal Representation',
            'description': 'عقد توكيل للتمثيل القانوني في القضايا المدنية والتجارية',
            'description_en': 'Legal representation contract for civil and commercial cases',
            'terms_and_conditions': '''1. يشمل التوكيل جميع مراحل التقاضي
2. يحق للمحامي اتخاذ جميع الإجراءات القانونية اللازمة
3. يلتزم العميل بتوفير جميع المستندات المطلوبة
4. يتم إشعار العميل بجميع التطورات''',
            'payment_terms': 'أتعاب ثابتة + نسبة من المبلغ المحصل',
            'start_date': date.today() - timedelta(days=30),
            'end_date': date.today() + timedelta(days=180),
            'duration_months': 6,
            'total_amount': Decimal('25000.00'),
            'currency': 'SAR',
            'status': 'active',
            'priority': 'medium'
        },
        {
            'title': 'عقد صياغة عقود تجارية',
            'title_en': 'Commercial Contract Drafting Agreement',
            'contract_type': 'صياغة عقود',
            'contract_type_en': 'Contract Drafting',
            'description': 'عقد صياغة ومراجعة العقود التجارية والاتفاقيات',
            'description_en': 'Contract for drafting and reviewing commercial contracts and agreements',
            'terms_and_conditions': '''1. صياغة العقود وفقاً للقوانين السارية
2. مراجعة قانونية شاملة للعقود
3. تقديم التعديلات المطلوبة
4. ضمان الحماية القانونية للعميل''',
            'payment_terms': 'دفع مقدم 60% والباقي عند التسليم',
            'start_date': date.today() - timedelta(days=15),
            'end_date': date.today() + timedelta(days=45),
            'duration_months': 2,
            'total_amount': Decimal('8000.00'),
            'currency': 'SAR',
            'status': 'draft',
            'priority': 'low'
        }
    ]

    for i, contract_data in enumerate(sample_contracts):
        # التحقق من عدم وجود عقد بنفس العنوان
        existing_contract = Contract.query.filter_by(title=contract_data['title']).first()
        if existing_contract:
            continue

        # اختيار عميل ومحامي
        client = clients[i % len(clients)]
        lawyer = lawyers[i % len(lawyers)] if lawyers else None

        # إنشاء رقم العقد
        contract_number = f"CON-{date.today().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

        contract = Contract(
            contract_number=contract_number,
            title=contract_data['title'],
            title_en=contract_data['title_en'],
            client_id=client.id,
            client_name=client.full_name,
            client_email=client.email,
            client_phone=client.phone,
            client_address=client.address,
            contract_type=contract_data['contract_type'],
            contract_type_en=contract_data['contract_type_en'],
            description=contract_data['description'],
            description_en=contract_data['description_en'],
            terms_and_conditions=contract_data['terms_and_conditions'],
            payment_terms=contract_data['payment_terms'],
            start_date=contract_data['start_date'],
            end_date=contract_data['end_date'],
            duration_months=contract_data['duration_months'],
            total_amount=contract_data['total_amount'],
            currency=contract_data['currency'],
            status=contract_data['status'],
            priority=contract_data['priority'],
            assigned_lawyer_id=lawyer.id if lawyer else None,
            created_by_id=admin_user.id,
            notes=f'عقد تجريبي رقم {i+1} لأغراض الاختبار',
            tags='تجريبي، اختبار، نموذج'
        )

        db.session.add(contract)

    db.session.commit()
    print("✅ تم إنشاء العقود التجريبية")

def create_sample_employees(db, models):
    """إنشاء موظفين وأقسام تجريبية"""
    Department = models.get('Department')
    Employee = models.get('Employee')

    if not all([Department, Employee]):
        print("⚠️  نماذج الموظفين غير متاحة")
        return

    from datetime import date
    import uuid

    # إنشاء الأقسام أولاً
    departments_data = [
        {
            'name': 'الشؤون القانونية',
            'name_en': 'Legal Affairs',
            'description': 'قسم الشؤون القانونية والاستشارات',
            'budget': 500000
        },
        {
            'name': 'الموارد البشرية',
            'name_en': 'Human Resources',
            'description': 'قسم إدارة الموارد البشرية والتوظيف',
            'budget': 300000
        },
        {
            'name': 'المالية والمحاسبة',
            'name_en': 'Finance & Accounting',
            'description': 'قسم الشؤون المالية والمحاسبة',
            'budget': 400000
        },
        {
            'name': 'تقنية المعلومات',
            'name_en': 'Information Technology',
            'description': 'قسم تقنية المعلومات والدعم التقني',
            'budget': 600000
        },
        {
            'name': 'الإدارة العامة',
            'name_en': 'General Administration',
            'description': 'قسم الإدارة العامة والخدمات المساندة',
            'budget': 250000
        }
    ]

    created_departments = []
    for dept_data in departments_data:
        existing_dept = Department.query.filter_by(name=dept_data['name']).first()
        if existing_dept:
            created_departments.append(existing_dept)
            continue

        department = Department(
            name=dept_data['name'],
            name_en=dept_data['name_en'],
            description=dept_data['description'],
            budget=dept_data['budget'],
            is_active=True
        )

        db.session.add(department)
        created_departments.append(department)

    db.session.commit()

    # إنشاء الموظفين
    employees_data = [
        {
            'full_name': 'أحمد محمد العلي',
            'full_name_en': 'Ahmed Mohammed Al-Ali',
            'national_id': '1234567890',
            'email': '<EMAIL>',
            'phone': '+************',
            'mobile': '+************',
            'address': 'الرياض، حي النخيل، شارع الملك فهد',
            'birth_date': date(1985, 5, 15),
            'gender': 'male',
            'nationality': 'سعودي',
            'marital_status': 'married',
            'department': 'الشؤون القانونية',
            'position': 'محامي أول',
            'position_en': 'Senior Lawyer',
            'employment_type': 'full_time',
            'hire_date': date(2020, 1, 15),
            'basic_salary': 15000,
            'allowances': 3000,
            'bank_account': '12**********3456',
            'bank_name': 'البنك الأهلي السعودي'
        },
        {
            'full_name': 'فاطمة سعد الزهراني',
            'full_name_en': 'Fatima Saad Al-Zahrani',
            'national_id': '**********',
            'email': '<EMAIL>',
            'phone': '+************',
            'mobile': '+************',
            'address': 'جدة، حي الحمراء، طريق الملك عبدالعزيز',
            'birth_date': date(1990, 8, 22),
            'gender': 'female',
            'nationality': 'سعودية',
            'marital_status': 'single',
            'department': 'الموارد البشرية',
            'position': 'مدير الموارد البشرية',
            'position_en': 'HR Manager',
            'employment_type': 'full_time',
            'hire_date': date(2019, 3, 10),
            'basic_salary': 12000,
            'allowances': 2500,
            'bank_account': '****************',
            'bank_name': 'بنك الراجحي'
        },
        {
            'full_name': 'خالد عبدالله المطيري',
            'full_name_en': 'Khalid Abdullah Al-Mutairi',
            'national_id': '**********',
            'email': '<EMAIL>',
            'phone': '+************',
            'mobile': '+************',
            'address': 'الدمام، حي الشاطئ، شارع الخليج العربي',
            'birth_date': date(1988, 12, 5),
            'gender': 'male',
            'nationality': 'سعودي',
            'marital_status': 'married',
            'department': 'المالية والمحاسبة',
            'position': 'محاسب أول',
            'position_en': 'Senior Accountant',
            'employment_type': 'full_time',
            'hire_date': date(2021, 6, 1),
            'basic_salary': 10000,
            'allowances': 2000,
            'bank_account': '**********345678',
            'bank_name': 'البنك السعودي للاستثمار'
        },
        {
            'full_name': 'نورا أحمد القحطاني',
            'full_name_en': 'Nora Ahmed Al-Qahtani',
            'national_id': '**********',
            'email': '<EMAIL>',
            'phone': '+************',
            'mobile': '+************',
            'address': 'الخبر، حي الراكة، شارع الأمير فيصل بن فهد',
            'birth_date': date(1992, 3, 18),
            'gender': 'female',
            'nationality': 'سعودية',
            'marital_status': 'single',
            'department': 'تقنية المعلومات',
            'position': 'مطور برمجيات',
            'position_en': 'Software Developer',
            'employment_type': 'full_time',
            'hire_date': date(2022, 9, 15),
            'basic_salary': 11000,
            'allowances': 1500,
            'bank_account': '**********456789',
            'bank_name': 'بنك سامبا'
        },
        {
            'full_name': 'محمد سالم العتيبي',
            'full_name_en': 'Mohammed Salem Al-Otaibi',
            'national_id': '**********',
            'email': '<EMAIL>',
            'phone': '+************',
            'mobile': '+************',
            'address': 'المدينة المنورة، حي العوالي، شارع سيد الشهداء',
            'birth_date': date(1987, 7, 10),
            'gender': 'male',
            'nationality': 'سعودي',
            'marital_status': 'married',
            'department': 'الإدارة العامة',
            'position': 'مدير إداري',
            'position_en': 'Administrative Manager',
            'employment_type': 'full_time',
            'hire_date': date(2018, 11, 20),
            'basic_salary': 13000,
            'allowances': 2200,
            'bank_account': '****************',
            'bank_name': 'البنك الأهلي التجاري'
        }
    ]

    for emp_data in employees_data:
        # التحقق من عدم وجود موظف بنفس رقم الهوية
        existing_employee = Employee.query.filter_by(national_id=emp_data['national_id']).first()
        if existing_employee:
            continue

        # البحث عن القسم
        department = next((d for d in created_departments if d.name == emp_data['department']), None)
        if not department:
            continue

        # إنشاء رقم الموظف
        employee_number = f"EMP-{date.today().strftime('%Y%m%d')}-{str(uuid.uuid4())[:6].upper()}"

        # حساب إجمالي الراتب
        total_salary = emp_data['basic_salary'] + emp_data['allowances']

        employee = Employee(
            employee_number=employee_number,
            full_name=emp_data['full_name'],
            full_name_en=emp_data['full_name_en'],
            national_id=emp_data['national_id'],
            email=emp_data['email'],
            phone=emp_data['phone'],
            mobile=emp_data['mobile'],
            address=emp_data['address'],
            birth_date=emp_data['birth_date'],
            gender=emp_data['gender'],
            nationality=emp_data['nationality'],
            marital_status=emp_data['marital_status'],
            department_id=department.id,
            position=emp_data['position'],
            position_en=emp_data['position_en'],
            employment_type=emp_data['employment_type'],
            employment_status='active',
            hire_date=emp_data['hire_date'],
            basic_salary=emp_data['basic_salary'],
            allowances=emp_data['allowances'],
            total_salary=total_salary,
            bank_account=emp_data['bank_account'],
            bank_name=emp_data['bank_name'],
            is_active=True
        )

        db.session.add(employee)

    db.session.commit()
    print("✅ تم إنشاء الأقسام والموظفين التجريبيين")

def reset_database():
    """إعادة تعيين قاعدة البيانات (حذف وإعادة إنشاء)"""
    print("⚠️  إعادة تعيين قاعدة البيانات...")
    
    db_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'legal_system.db')
    if os.path.exists(db_file):
        os.remove(db_file)
        print("🗑️  تم حذف قاعدة البيانات القديمة")
    
    init_database()

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) > 1 and sys.argv[1] == '--reset':
        reset_database()
    else:
        init_database()

if __name__ == '__main__':
    main()
