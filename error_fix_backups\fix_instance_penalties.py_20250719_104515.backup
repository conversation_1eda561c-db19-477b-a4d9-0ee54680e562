#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جدول الجزاءات في instance/legal_system.db
"""

import sqlite3
import os

def fix_instance_penalties():
    """إصلاح جدول الجزاءات في instance/legal_system.db"""
    
    db_path = 'instance/legal_system.db'
    
    if not os.path.exists(db_path):
        print(f"❌ ملف قاعدة البيانات غير موجود: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"🔍 فحص قاعدة البيانات: {db_path}")
        
        # التحقق من وجود جدول الجزاءات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='penalties'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ جدول الجزاءات غير موجود")
            return False
        
        # فحص الأعمدة الحالية
        cursor.execute("PRAGMA table_info(penalties)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        print(f"📋 الأعمدة الموجودة: {', '.join(column_names)}")
        
        # التحقق من وجود عمود category
        if 'category' in column_names:
            print("✅ عمود category موجود بالفعل")
            return True
        
        print("🔄 إضافة عمود category...")
        
        # إضافة عمود category
        cursor.execute("""
            ALTER TABLE penalties 
            ADD COLUMN category VARCHAR(50) NOT NULL DEFAULT 'general'
        """)
        
        print("✅ تم إضافة عمود category بنجاح")
        
        # التحقق من النتيجة
        cursor.execute("PRAGMA table_info(penalties)")
        new_columns = cursor.fetchall()
        new_column_names = [column[1] for column in new_columns]
        
        print(f"📋 الأعمدة بعد التحديث: {', '.join(new_column_names)}")
        
        # حفظ التغييرات
        conn.commit()
        
        # عرض عدد السجلات
        cursor.execute("SELECT COUNT(*) FROM penalties")
        count = cursor.fetchone()[0]
        print(f"📊 عدد سجلات الجزاءات: {count}")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 بدء إصلاح جدول الجزاءات في instance/legal_system.db...")
    print("=" * 60)
    
    success = fix_instance_penalties()
    
    print("=" * 60)
    if success:
        print("✅ تم إصلاح جدول الجزاءات بنجاح")
    else:
        print("❌ فشل في إصلاح جدول الجزاءات")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
