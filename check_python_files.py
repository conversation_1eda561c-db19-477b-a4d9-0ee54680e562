#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جميع ملفات Python للبحث عن أخطاء
"""

import os
import ast
import sys
import importlib.util

def check_python_syntax(file_path):
    """فحص صحة بناء الجملة في ملف Python"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص بناء الجملة
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, f"خطأ في بناء الجملة: {e}"
    except Exception as e:
        return False, f"خطأ في القراءة: {e}"

def check_imports(file_path):
    """فحص الاستيرادات في ملف Python"""
    try:
        spec = importlib.util.spec_from_file_location("module", file_path)
        if spec is None:
            return False, "لا يمكن تحميل المواصفات"
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return True, None
    except ImportError as e:
        return False, f"خطأ في الاستيراد: {e}"
    except Exception as e:
        return False, f"خطأ في التحميل: {e}"

def find_python_files():
    """البحث عن جميع ملفات Python"""
    python_files = []
    
    # الملفات في المجلد الرئيسي
    for file in os.listdir('.'):
        if file.endswith('.py') and not file.startswith('check_') and not file.startswith('test_'):
            python_files.append(file)
    
    # الملفات في مجلد routes
    if os.path.exists('routes'):
        for file in os.listdir('routes'):
            if file.endswith('.py') and file != '__init__.py':
                python_files.append(os.path.join('routes', file))
    
    return python_files

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص ملفات Python...")
    print("=" * 50)
    
    python_files = find_python_files()
    
    syntax_errors = []
    import_errors = []
    
    for file_path in python_files:
        print(f"\n📄 فحص {file_path}...")
        
        # فحص بناء الجملة
        syntax_ok, syntax_error = check_python_syntax(file_path)
        if not syntax_ok:
            syntax_errors.append((file_path, syntax_error))
            print(f"  ❌ خطأ في بناء الجملة: {syntax_error}")
        else:
            print(f"  ✅ بناء الجملة صحيح")
        
        # فحص الاستيرادات (فقط إذا كان بناء الجملة صحيح)
        if syntax_ok:
            import_ok, import_error = check_imports(file_path)
            if not import_ok:
                import_errors.append((file_path, import_error))
                print(f"  ⚠️ مشكلة في الاستيراد: {import_error}")
            else:
                print(f"  ✅ الاستيرادات صحيحة")
    
    # ملخص النتائج
    print("\n" + "=" * 50)
    print("📋 ملخص النتائج:")
    
    if syntax_errors:
        print(f"\n❌ أخطاء بناء الجملة ({len(syntax_errors)}):")
        for file_path, error in syntax_errors:
            print(f"  - {file_path}: {error}")
    
    if import_errors:
        print(f"\n⚠️ مشاكل الاستيراد ({len(import_errors)}):")
        for file_path, error in import_errors:
            print(f"  - {file_path}: {error}")
    
    if not syntax_errors and not import_errors:
        print("✅ جميع ملفات Python سليمة!")
        return True
    else:
        print(f"\n📊 الإحصائيات:")
        print(f"  - إجمالي الملفات: {len(python_files)}")
        print(f"  - ملفات سليمة: {len(python_files) - len(syntax_errors) - len(import_errors)}")
        print(f"  - أخطاء بناء الجملة: {len(syntax_errors)}")
        print(f"  - مشاكل الاستيراد: {len(import_errors)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
