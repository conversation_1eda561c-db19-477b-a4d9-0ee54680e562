"""
نظام إدارة الصلاحيات لنظام الشؤون القانونية
"""

from functools import wraps
from flask import flash, redirect, url_for, abort
from flask_login import current_user

# تعريف الصلاحيات الأساسية
PERMISSIONS = {
    # صلاحيات إدارة المستخدمين
    'users': {
        'view_users': 'عرض المستخدمين',
        'create_users': 'إضافة مستخدمين',
        'edit_users': 'تعديل المستخدمين',
        'delete_users': 'حذف المستخدمين',
        'manage_user_permissions': 'إدارة صلاحيات المستخدمين',
        'view_user_activity': 'عرض نشاط المستخدمين'
    },
    
    # صلاحيات إدارة القضايا
    'cases': {
        'view_cases': 'عرض القضايا',
        'create_cases': 'إضافة قضايا',
        'edit_cases': 'تعديل القضايا',
        'delete_cases': 'حذف القضايا',
        'assign_cases': 'تعيين القضايا',
        'view_all_cases': 'عرض جميع القضايا',
        'manage_case_documents': 'إدارة مستندات القضايا',
        'manage_case_sessions': 'إدارة جلسات القضايا'
    },
    
    # صلاحيات إدارة العملاء
    'clients': {
        'view_clients': 'عرض العملاء',
        'create_clients': 'إضافة عملاء',
        'edit_clients': 'تعديل العملاء',
        'delete_clients': 'حذف العملاء',
        'view_client_cases': 'عرض قضايا العملاء',
        'manage_client_documents': 'إدارة مستندات العملاء'
    },
    
    # صلاحيات إدارة المحامين
    'lawyers': {
        'view_lawyers': 'عرض المحامين',
        'create_lawyers': 'إضافة محامين',
        'edit_lawyers': 'تعديل المحامين',
        'delete_lawyers': 'حذف المحامين',
        'view_lawyer_performance': 'عرض أداء المحامين'
    },
    
    # صلاحيات إدارة المواعيد
    'appointments': {
        'view_appointments': 'عرض المواعيد',
        'create_appointments': 'إضافة مواعيد',
        'edit_appointments': 'تعديل المواعيد',
        'delete_appointments': 'حذف المواعيد',
        'view_all_appointments': 'عرض جميع المواعيد'
    },
    
    # صلاحيات إدارة المستندات
    'documents': {
        'view_documents': 'عرض المستندات',
        'upload_documents': 'رفع مستندات',
        'edit_documents': 'تعديل المستندات',
        'delete_documents': 'حذف المستندات',
        'download_documents': 'تحميل المستندات'
    },

    # صلاحيات إدارة العقود
    'contracts': {
        'view_contracts': 'عرض العقود',
        'create_contracts': 'إنشاء عقود',
        'edit_contracts': 'تعديل العقود',
        'delete_contracts': 'حذف العقود',
        'download_contracts': 'تحميل ملفات العقود',
        'manage_contract_files': 'إدارة ملفات العقود',
        'view_contract_reports': 'عرض تقارير العقود',
        'export_contracts': 'تصدير العقود'
    },

    # صلاحيات إدارة الموظفين
    'employees': {
        'view_employees': 'عرض الموظفين',
        'create_employees': 'إضافة موظفين جدد',
        'edit_employees': 'تعديل بيانات الموظفين',
        'delete_employees': 'حذف الموظفين',
        'view_employee_details': 'عرض تفاصيل الموظف',
        'manage_employee_documents': 'إدارة مستندات الموظفين',
        'view_employee_reports': 'عرض تقارير الموظفين',
        'export_employee_data': 'تصدير بيانات الموظفين'
    },

    # صلاحيات الأقسام
    'departments': {
        'view_departments': 'عرض الأقسام',
        'create_departments': 'إنشاء أقسام جديدة',
        'edit_departments': 'تعديل الأقسام',
        'delete_departments': 'حذف الأقسام'
    },



    # صلاحيات إدارة الفواتير
    'invoices': {
        'view_invoices': 'عرض الفواتير',
        'create_invoices': 'إنشاء فواتير',
        'edit_invoices': 'تعديل الفواتير',
        'delete_invoices': 'حذف الفواتير',
        'manage_payments': 'إدارة المدفوعات',
        'view_financial_reports': 'عرض التقارير المالية'
    },
    
    # صلاحيات إدارة التقارير
    'reports': {
        'view_reports': 'عرض التقارير',
        'create_reports': 'إنشاء تقارير',
        'export_reports': 'تصدير التقارير',
        'view_analytics': 'عرض التحليلات'
    },

    # صلاحيات الحضور والغياب
    'attendance': {
        'view_attendance': 'عرض سجلات الحضور',
        'manage_attendance': 'إدارة سجلات الحضور',
        'checkin_checkout': 'تسجيل الحضور والانصراف',
        'view_attendance_reports': 'عرض تقارير الحضور'
    },

    # صلاحيات الإجازات والاستئذان
    'leaves': {
        'view_leaves': 'عرض طلبات الإجازات',
        'manage_leaves': 'إدارة طلبات الإجازات',
        'approve_leaves': 'الموافقة على الإجازات',
        'view_leave_reports': 'عرض تقارير الإجازات'
    },

    # صلاحيات الإنذارات
    'warnings': {
        'view_warnings': 'عرض الإنذارات',
        'manage_warnings': 'إدارة الإنذارات',
        'issue_warnings': 'إصدار إنذارات',
        'view_warning_reports': 'عرض تقارير الإنذارات'
    },

    # صلاحيات الجزاءات
    'penalties': {
        'view_penalties': 'عرض الجزاءات',
        'manage_penalties': 'إدارة الجزاءات',
        'issue_penalties': 'إصدار جزاءات',
        'manage_penalty_payments': 'إدارة مدفوعات الجزاءات',
        'view_penalty_reports': 'عرض تقارير الجزاءات'
    },
    
    # صلاحيات إدارة النظام
    'system': {
        'view_admin_panel': 'عرض لوحة الإدارة',
        'manage_system_settings': 'إدارة إعدادات النظام',
        'manage_company_settings': 'إدارة إعدادات الشركة',
        'manage_report_settings': 'إدارة إعدادات التقارير',
        'upload_company_logo': 'رفع شعار الشركة',
        'reset_system_settings': 'إعادة تعيين الإعدادات',
        'manage_backups': 'إدارة النسخ الاحتياطية',
        'view_system_logs': 'عرض سجلات النظام',
        'manage_roles': 'إدارة الأدوار',
        'manage_permissions': 'إدارة الصلاحيات'
    }
}

# تعريف الأدوار الافتراضية
DEFAULT_ROLES = {
    'admin': {
        'display_name': 'مدير النظام',
        'description': 'صلاحيات كاملة لإدارة النظام',
        'permissions': 'all'  # جميع الصلاحيات
    },
    'lawyer': {
        'display_name': 'محامي',
        'description': 'صلاحيات المحامي للعمل على القضايا والعقود',
        'permissions': [
            'view_cases', 'create_cases', 'edit_cases', 'manage_case_documents', 'manage_case_sessions',
            'view_clients', 'create_clients', 'edit_clients', 'view_client_cases', 'manage_client_documents',
            'view_appointments', 'create_appointments', 'edit_appointments',
            'view_documents', 'upload_documents', 'edit_documents', 'download_documents',
            'view_contracts', 'create_contracts', 'edit_contracts', 'download_contracts', 'manage_contract_files',
            'view_invoices', 'create_invoices', 'edit_invoices',
            'view_reports', 'view_contract_reports'
        ]
    },
    'secretary': {
        'display_name': 'سكرتير',
        'description': 'صلاحيات السكرتير لإدارة المواعيد والمستندات والعقود والعملاء',
        'permissions': [
            'view_cases', 'view_clients', 'create_clients', 'edit_clients', 'manage_client_documents',
            'view_appointments', 'create_appointments', 'edit_appointments', 'delete_appointments',
            'view_documents', 'upload_documents', 'download_documents',
            'view_contracts', 'create_contracts', 'edit_contracts', 'download_contracts',
            'view_invoices'
        ]
    },
    'hr_manager': {
        'display_name': 'مدير الموارد البشرية',
        'description': 'صلاحيات إدارة الموظفين والحضور والجزاءات',
        'permissions': [
            'view_employees', 'create_employees', 'edit_employees', 'delete_employees', 'manage_employee_documents',
            'view_departments', 'create_departments', 'edit_departments', 'delete_departments',
            'view_attendance', 'manage_attendance', 'checkin_checkout', 'view_attendance_reports',
            'view_leaves', 'manage_leaves', 'approve_leaves', 'view_leave_reports',
            'view_warnings', 'manage_warnings', 'issue_warnings', 'view_warning_reports',
            'view_penalties', 'manage_penalties', 'issue_penalties', 'manage_penalty_payments', 'view_penalty_reports',
            'view_employee_reports', 'export_employee_data'
        ]
    },
    'hr_assistant': {
        'display_name': 'مساعد الموارد البشرية',
        'description': 'صلاحيات محدودة لإدارة الموظفين',
        'permissions': [
            'view_employees', 'view_employee_details', 'manage_employee_documents',
            'view_departments',
            'view_attendance', 'manage_attendance', 'checkin_checkout', 'view_attendance_reports',
            'view_leaves', 'manage_leaves', 'view_leave_reports',
            'view_warnings', 'view_warning_reports',
            'view_penalties', 'view_penalty_reports',
            'view_employee_reports'
        ]
    },
    'client': {
        'display_name': 'عميل',
        'description': 'صلاحيات العميل لعرض قضاياه ومستنداته',
        'permissions': [
            'view_client_cases', 'view_documents', 'download_documents',
            'view_appointments'
        ]
    }
}

def permission_required(permission_name):
    """ديكوريتر للتحقق من وجود صلاحية معينة"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('يجب تسجيل الدخول أولاً', 'error')
                return redirect(url_for('login'))
            
            if not current_user.has_permission(permission_name):
                flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
                return abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def any_permission_required(permission_names):
    """ديكوريتر للتحقق من وجود أي من الصلاحيات المحددة"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('يجب تسجيل الدخول أولاً', 'error')
                return redirect(url_for('login'))
            
            if not current_user.has_any_permission(permission_names):
                flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
                return abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات المدير (محدث)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('يجب تسجيل الدخول أولاً', 'error')
            return redirect(url_for('login'))
        
        # التحقق من الدور القديم أو الصلاحية الجديدة
        if (current_user.role == 'admin' or 
            current_user.has_permission('view_admin_panel')):
            return f(*args, **kwargs)
        
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return abort(403)
    
    return decorated_function

def get_all_permissions():
    """الحصول على جميع الصلاحيات المتاحة"""
    all_permissions = []
    for category, permissions in PERMISSIONS.items():
        for perm_name, perm_display in permissions.items():
            all_permissions.append({
                'name': perm_name,
                'display_name': perm_display,
                'category': category
            })
    return all_permissions

def get_permissions_by_category():
    """الحصول على الصلاحيات مجمعة حسب الفئة"""
    return PERMISSIONS

def initialize_permissions(db, models):
    """تهيئة الصلاحيات والأدوار الافتراضية في قاعدة البيانات"""
    Permission = models['Permission']
    Role = models['Role']
    
    # إنشاء الصلاحيات
    for category, permissions in PERMISSIONS.items():
        for perm_name, perm_display in permissions.items():
            existing_permission = Permission.query.filter_by(name=perm_name).first()
            if not existing_permission:
                permission = Permission(
                    name=perm_name,
                    display_name=perm_display,
                    category=category
                )
                db.session.add(permission)
    
    # إنشاء الأدوار
    for role_name, role_data in DEFAULT_ROLES.items():
        existing_role = Role.query.filter_by(name=role_name).first()
        if not existing_role:
            role = Role(
                name=role_name,
                display_name=role_data['display_name'],
                description=role_data['description'],
                is_default=True
            )
            db.session.add(role)
            db.session.flush()  # للحصول على ID
            
            # إضافة الصلاحيات للدور
            if role_data['permissions'] == 'all':
                # إضافة جميع الصلاحيات للمدير
                all_permissions = Permission.query.all()
                role.permissions.extend(all_permissions)
            else:
                # إضافة صلاحيات محددة
                for perm_name in role_data['permissions']:
                    permission = Permission.query.filter_by(name=perm_name).first()
                    if permission:
                        role.permissions.append(permission)
    
    db.session.commit()
    print("✅ تم تهيئة الصلاحيات والأدوار بنجاح")
