#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من إصلاح ملف users.html
"""

def check_users_html():
    """فحص ملف users.html للتأكد من الإصلاحات"""
    
    file_path = 'templates/admin/users.html'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔍 فحص ملف users.html...")
        
        issues_found = []
        fixes_verified = []
        
        # فحص استخدام alert() القديم
        if "alert('سيتم إضافة هذه الوظيفة قريباً')" in content:
            issues_found.append("لا يزال يستخدم alert() في التنقل")
        else:
            fixes_verified.append("تم استبدال alert() بـ showAlert() في التنقل")
        
        # فحص استخدام alert() في JavaScript
        alert_count = content.count('alert(')
        if alert_count > 0:
            issues_found.append(f"لا يزال يستخدم alert() في {alert_count} مكان")
        else:
            fixes_verified.append("تم استبدال جميع استخدامات alert()")
        
        # فحص showAlert()
        showAlert_count = content.count('showAlert(')
        if showAlert_count > 0:
            fixes_verified.append(f"تم استخدام showAlert() في {showAlert_count} مكان")
        
        # فحص مشاكل علامات الاقتباس
        if "deleteUser({{ user.id }}, '{{ user.full_name }}'" in content:
            issues_found.append("مشكلة في علامات الاقتباس في deleteUser")
        else:
            fixes_verified.append("تم إصلاح علامات الاقتباس في deleteUser")
        
        # فحص modalTitle
        if 'getElementById("userModalLabel")' in content:
            issues_found.append("لا يزال يستخدم userModalLabel غير الموجود")
        else:
            fixes_verified.append("تم إصلاح مرجع modalTitle")
        
        # فحص بناء الجملة العام
        try:
            # فحص أساسي للقوالب
            if content.count('{{') != content.count('}}'):
                issues_found.append('عدم تطابق علامات Jinja2')
            else:
                fixes_verified.append("علامات Jinja2 متطابقة")
            
            if content.count('{%') != content.count('%}'):
                issues_found.append('عدم تطابق علامات Jinja2 للتحكم')
            else:
                fixes_verified.append("علامات التحكم Jinja2 متطابقة")
                
        except Exception as e:
            issues_found.append(f"خطأ في فحص القالب: {e}")
        
        # عرض النتائج
        print("\n✅ الإصلاحات المؤكدة:")
        for fix in fixes_verified:
            print(f"  ✓ {fix}")
        
        if issues_found:
            print("\n❌ مشاكل لا تزال موجودة:")
            for issue in issues_found:
                print(f"  ✗ {issue}")
            return False
        else:
            print("\n🎉 جميع الإصلاحات تمت بنجاح!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 التحقق من إصلاحات ملف users.html...")
    print("=" * 50)
    
    success = check_users_html()
    
    print("=" * 50)
    if success:
        print("✅ تم إصلاح جميع المشاكل في users.html بنجاح!")
    else:
        print("⚠️ لا تزال هناك مشاكل تحتاج إلى إصلاح")
    
    return success

if __name__ == "__main__":
    main()
