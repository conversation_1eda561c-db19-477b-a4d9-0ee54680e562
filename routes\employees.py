from django.shortcuts import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, send_file
from flask_login import login_required, current_user
from datetime import datetime, date
from sqlalchemy import or_, func, and_
import uuid
import os
from werkzeug.utils import secure_filename
from permissions_manager import permission_required, any_permission_required

bp = Blueprint('employees', __name__, url_prefix='/employees')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from django.shortcuts import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        return {}

def get_db():
    """الحصول على كائن قاعدة البيانات"""
    try:
        from django.shortcuts import current_app
        return current_app.extensions['sqlalchemy'].db
    except (RuntimeError, KeyError):
        return None

def allowed_file(filename):
    """التحقق من أن نوع الملف مسموح"""
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    """التأكد من وجود مجلد الرفع"""
    upload_folder = os.path.join(current_app.root_path, 'uploads', 'employees')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    return upload_folder

@bp.route('/')
@login_required
@permission_required('view_employees')
def index():
    """عرض جميع الموظفين"""
    try:
        models = get_models()
        Employee = models.get('Employee')
        Department = models.get('Department')
        db = get_db()
        
        if not all([Employee, Department, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # معاملات البحث والفلترة
        page = request.GET.get('page', 1, type=int)
        search = request.GET.get('search', '')
        department_id = request.GET.get('department', '', type=str)
        employment_status = request.GET.get('status', '')
        employment_type = request.GET.get('type', '')
        sort_by = request.GET.get('sort', 'created_at')
        sort_order = request.GET.get('order', 'desc')

        # بناء الاستعلام الأساسي
        query = Employee.query

        # البحث النصي
        if search:
            query = query.filter(
                or_(
                    Employee.full_name.contains(search),
                    Employee.employee_number.contains(search),
                    Employee.email.contains(search),
                    Employee.phone.contains(search),
                    Employee.national_id.contains(search),
                    Employee.position.contains(search)
                )
            )

        # فلترة حسب القسم
        if department_id:
            query = query.filter(Employee.department_id == department_id)

        # فلترة حسب حالة التوظيف
        if employment_status:
            query = query.filter(Employee.employment_status == employment_status)

        # فلترة حسب نوع التوظيف
        if employment_type:
            query = query.filter(Employee.employment_type == employment_type)

        # ترتيب النتائج
        if sort_by == 'name':
            if sort_order == 'asc':
                query = query.order_by(Employee.full_name.asc())
            else:
                query = query.order_by(Employee.full_name.desc())
        elif sort_by == 'employee_number':
            if sort_order == 'asc':
                query = query.order_by(Employee.employee_number.asc())
            else:
                query = query.order_by(Employee.employee_number.desc())
        elif sort_by == 'hire_date':
            if sort_order == 'asc':
                query = query.order_by(Employee.hire_date.asc())
            else:
                query = query.order_by(Employee.hire_date.desc())
        else:  # created_at
            if sort_order == 'asc':
                query = query.order_by(Employee.created_at.asc())
            else:
                query = query.order_by(Employee.created_at.desc())

        # تطبيق pagination
        per_page = 15
        employees = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )

        # جلب الأقسام للفلترة
        departments = Department.query.filter_by(is_active=True).all()

        # إحصائيات عامة
        stats = {
            'total': Employee.query.count(),
            'active': Employee.query.filter_by(employment_status='active').count(),
            'inactive': Employee.query.filter(Employee.employment_status.in_(['inactive', 'terminated', 'resigned'])).count(),
            'full_time': Employee.query.filter_by(employment_type='full_time').count(),
            'part_time': Employee.query.filter_by(employment_type='part_time').count(),
            'contract': Employee.query.filter_by(employment_type='contract').count(),
            'departments_count': Department.query.filter_by(is_active=True).count()
        }

        return render('employees/index.html',
                             employees=employees,
                             departments=departments,
                             search=search,
                             department_id=department_id,
                             employment_status=employment_status,
                             employment_type=employment_type,
                             sort_by=sort_by,
                             sort_order=sort_order,
                             stats=stats,
                             # متغيرات رأس التقرير
                             report_title="تقرير قائمة الموظفين",
                             report_date=datetime.now().strftime('%Y-%m-%d'),
                             # متغيرات إضافية لرأس التقرير
                             show_logo='true',
                             show_company_info='true',
                             show_print_time='true',
                             logo_path='images/logo.svg',
                             company_name='مكتب الشؤون القانونية',
                             company_subtitle='نظام إدارة الموارد البشرية والشؤون القانونية')

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('create_employees')
def add():
    """إضافة موظف جديد"""
    try:
        models = get_models()
        Employee = models.get('Employee')
        Department = models.get('Department')
        db = get_db()
        
        if not all([Employee, Department, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('employees.index'))

        if request.method == 'POST':
            # جلب البيانات من النموذج
            full_name = request.POST.get('full_name', '').strip()
            full_name_en = request.POST.get('full_name_en', '').strip()
            national_id = request.POST.get('national_id', '').strip()
            email = request.POST.get('email', '').strip()
            phone = request.POST.get('phone', '').strip()
            mobile = request.POST.get('mobile', '').strip()
            address = request.POST.get('address', '').strip()
            birth_date = request.POST.get('birth_date')
            gender = request.POST.get('gender')
            nationality = request.POST.get('nationality', '').strip()
            marital_status = request.POST.get('marital_status')
            
            # البيانات الوظيفية
            department_id = request.POST.get('department_id', type=int)
            position = request.POST.get('position', '').strip()
            position_en = request.POST.get('position_en', '').strip()
            employment_type = request.POST.get('employment_type')
            hire_date = request.POST.get('hire_date')
            
            # البيانات المالية
            basic_salary = request.POST.get('basic_salary', type=float)
            allowances = request.POST.get('allowances', type=float) or 0
            deductions = request.POST.get('deductions', type=float) or 0
            bank_account = request.POST.get('bank_account', '').strip()
            bank_name = request.POST.get('bank_name', '').strip()
            
            # التحقق من البيانات المطلوبة
            if not all([full_name, department_id, position, employment_type, hire_date]):
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                departments = Department.query.filter_by(is_active=True).all()
                return render('employees/add.html', departments=departments)
            
            # التحقق من عدم تكرار رقم الهوية
            if national_id and Employee.query.filter_by(national_id=national_id).first():
                flash('رقم الهوية مستخدم بالفعل', 'error')
                departments = Department.query.filter_by(is_active=True).all()
                return render('employees/add.html', departments=departments)
            
            # التحقق من عدم تكرار البريد الإلكتروني
            if email and Employee.query.filter_by(email=email).first():
                flash('البريد الإلكتروني مستخدم بالفعل', 'error')
                departments = Department.query.filter_by(is_active=True).all()
                return render('employees/add.html', departments=departments)
            
            # إنشاء رقم الموظف
            employee_number = f"EMP-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:6].upper()}"
            
            # حساب إجمالي الراتب
            total_salary = (basic_salary or 0) + allowances - deductions
            
            # تحويل التواريخ
            birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d').date() if birth_date else None
            hire_date_obj = datetime.strptime(hire_date, '%Y-%m-%d').date()
            
            # إنشاء الموظف الجديد
            new_employee = Employee(
                employee_number=employee_number,
                full_name=full_name,
                full_name_en=full_name_en if full_name_en else None,
                national_id=national_id if national_id else None,
                email=email if email else None,
                phone=phone if phone else None,
                mobile=mobile if mobile else None,
                address=address if address else None,
                birth_date=birth_date_obj,
                gender=gender,
                nationality=nationality if nationality else None,
                marital_status=marital_status,
                department_id=department_id,
                position=position,
                position_en=position_en if position_en else None,
                employment_type=employment_type,
                employment_status='active',
                hire_date=hire_date_obj,
                basic_salary=basic_salary,
                allowances=allowances,
                deductions=deductions,
                total_salary=total_salary,
                bank_account=bank_account if bank_account else None,
                bank_name=bank_name if bank_name else None,
                is_active=True,
                created_by=current_user.id
            )
            
            db.add(new_employee)
            db.save()
            
            flash('تم إضافة الموظف بنجاح', 'success')
            return redirect(url_for('employees.view', id=new_employee.id))

        # جلب الأقسام للنموذج
        departments = Department.query.filter_by(is_active=True).all()
        return render('employees/add.html', departments=departments)

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('employees.index'))

@bp.route('/<int:id>')
@login_required
@permission_required('view_employees')
def view(id):
    """عرض تفاصيل الموظف"""
    try:
        models = get_models()
        Employee = models.get('Employee')
        Attendance = models.get('Attendance')
        Penalty = models.get('Penalty')
        Warning = models.get('Warning')
        EmployeeDocument = models.get('EmployeeDocument')
        db = get_db()
        
        if not all([Employee, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('employees.index'))

        # جلب الموظف
        employee = Employee.query.get_or_404(id)
        
        # جلب سجلات الحضور الأخيرة
        recent_attendance = []
        if Attendance:
            recent_attendance = Attendance.query.filter_by(employee_id=id).order_by(Attendance.date.desc()).limit(10).all()
        
        # جلب الجزاءات
        penalties = []
        if Penalty:
            penalties = Penalty.query.filter_by(employee_id=id).order_by(Penalty.penalty_date.desc()).all()
        
        # جلب الإنذارات
        warnings = []
        if Warning:
            warnings = Warning.query.filter_by(employee_id=id).order_by(Warning.warning_date.desc()).all()
        
        # جلب المستندات
        documents = []
        if EmployeeDocument:
            documents = EmployeeDocument.query.filter_by(employee_id=id).order_by(EmployeeDocument.created_at.desc()).limit(10).all()
        
        # حساب الإحصائيات
        stats = {
            'attendance_count': len(recent_attendance),
            'penalties_count': len(penalties),
            'warnings_count': len(warnings),
            'documents_count': len(documents),
            'active_penalties': len([p for p in penalties if p.status == 'active']),
            'active_warnings': len([w for w in warnings if w.status == 'active'])
        }

        return render('employees/view.html',
                             employee=employee,
                             recent_attendance=recent_attendance,
                             penalties=penalties,
                             warnings=warnings,
                             documents=documents,
                             stats=stats,
                             # متغيرات رأس التقرير
                             report_title=f"تقرير تفاصيل الموظف: {employee.full_name}",
                             report_date=datetime.now().strftime('%Y-%m-%d'),
                             # متغيرات إضافية لرأس التقرير
                             show_logo='true',
                             show_company_info='true',
                             show_print_time='true',
                             logo_path='images/logo.svg',
                             company_name='مكتب الشؤون القانونية',
                             company_subtitle='نظام إدارة الموارد البشرية والشؤون القانونية')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('employees.index'))

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('edit_employees')
def edit(id):
    """تعديل الموظف"""
    try:
        models = get_models()
        Employee = models.get('Employee')
        Department = models.get('Department')
        db = get_db()

        if not all([Employee, Department, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('employees.index'))

        employee = Employee.query.get_or_404(id)

        if request.method == 'POST':
            # جلب البيانات من النموذج
            full_name = request.POST.get('full_name', '').strip()
            full_name_en = request.POST.get('full_name_en', '').strip()
            national_id = request.POST.get('national_id', '').strip()
            email = request.POST.get('email', '').strip()
            phone = request.POST.get('phone', '').strip()
            mobile = request.POST.get('mobile', '').strip()
            address = request.POST.get('address', '').strip()
            birth_date = request.POST.get('birth_date')
            gender = request.POST.get('gender')
            nationality = request.POST.get('nationality', '').strip()
            marital_status = request.POST.get('marital_status')

            # البيانات الوظيفية
            department_id = request.POST.get('department_id', type=int)
            position = request.POST.get('position', '').strip()
            position_en = request.POST.get('position_en', '').strip()
            employment_type = request.POST.get('employment_type')
            employment_status = request.POST.get('employment_status')
            hire_date = request.POST.get('hire_date')
            termination_date = request.POST.get('termination_date')

            # البيانات المالية
            basic_salary = request.POST.get('basic_salary', type=float)
            allowances = request.POST.get('allowances', type=float) or 0
            deductions = request.POST.get('deductions', type=float) or 0
            bank_account = request.POST.get('bank_account', '').strip()
            bank_name = request.POST.get('bank_name', '').strip()

            is_active = request.POST.get('is_active') == 'on'

            # التحقق من البيانات المطلوبة
            if not all([full_name, department_id, position, employment_type, hire_date]):
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                departments = Department.query.filter_by(is_active=True).all()
                return render('employees/edit.html', employee=employee, departments=departments)

            # التحقق من عدم تكرار رقم الهوية (إذا تم تغييره)
            if national_id and national_id != employee.national_id:
                existing_employee = Employee.query.filter_by(national_id=national_id).first()
                if existing_employee:
                    flash('رقم الهوية مستخدم بالفعل', 'error')
                    departments = Department.query.filter_by(is_active=True).all()
                    return render('employees/edit.html', employee=employee, departments=departments)

            # التحقق من عدم تكرار البريد الإلكتروني (إذا تم تغييره)
            if email and email != employee.email:
                existing_employee = Employee.query.filter_by(email=email).first()
                if existing_employee:
                    flash('البريد الإلكتروني مستخدم بالفعل', 'error')
                    departments = Department.query.filter_by(is_active=True).all()
                    return render('employees/edit.html', employee=employee, departments=departments)

            # حساب إجمالي الراتب
            total_salary = (basic_salary or 0) + allowances - deductions

            # تحويل التواريخ
            birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d').date() if birth_date else None
            hire_date_obj = datetime.strptime(hire_date, '%Y-%m-%d').date()
            termination_date_obj = datetime.strptime(termination_date, '%Y-%m-%d').date() if termination_date else None

            # تحديث بيانات الموظف
            employee.full_name = full_name
            employee.full_name_en = full_name_en if full_name_en else None
            employee.national_id = national_id if national_id else None
            employee.email = email if email else None
            employee.phone = phone if phone else None
            employee.mobile = mobile if mobile else None
            employee.address = address if address else None
            employee.birth_date = birth_date_obj
            employee.gender = gender
            employee.nationality = nationality if nationality else None
            employee.marital_status = marital_status
            employee.department_id = department_id
            employee.position = position
            employee.position_en = position_en if position_en else None
            employee.employment_type = employment_type
            employee.employment_status = employment_status
            employee.hire_date = hire_date_obj
            employee.termination_date = termination_date_obj
            employee.basic_salary = basic_salary
            employee.allowances = allowances
            employee.deductions = deductions
            employee.total_salary = total_salary
            employee.bank_account = bank_account if bank_account else None
            employee.bank_name = bank_name if bank_name else None
            employee.is_active = is_active
            employee.updated_at = datetime.utcnow()

            db.save()

            flash('تم تحديث الموظف بنجاح', 'success')
            return redirect(url_for('employees.view', id=id))

        # جلب الأقسام للنموذج
        departments = Department.query.filter_by(is_active=True).all()
        return render('employees/edit.html', employee=employee, departments=departments)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('employees.index'))

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@permission_required('delete_employees')
def delete(id):
    """حذف الموظف (إلغاء تفعيل)"""
    try:
        models = get_models()
        Employee = models.get('Employee')
        db = get_db()

        if not all([Employee, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        employee = Employee.query.get_or_404(id)

        # إلغاء تفعيل الموظف بدلاً من حذفه نهائياً
        employee.is_active = False
        employee.employment_status = 'terminated'
        employee.termination_date = date.today()
        employee.updated_at = datetime.utcnow()

        db.save()

        return JsonResponse({'success': True, 'message': 'تم إنهاء خدمة الموظف بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:id>/activate', methods=['POST'])
@login_required
@permission_required('edit_employees')
def activate(id):
    """تفعيل الموظف"""
    try:
        models = get_models()
        Employee = models.get('Employee')
        db = get_db()

        if not all([Employee, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        employee = Employee.query.get_or_404(id)

        employee.is_active = True
        employee.employment_status = 'active'
        employee.termination_date = None
        employee.updated_at = datetime.utcnow()

        db.save()

        return JsonResponse({'success': True, 'message': 'تم تفعيل الموظف بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:id>/documents/test')
@login_required
def documents_test(id):
    """اختبار مسار المستندات"""
    return f"مسار المستندات يعمل للموظف رقم {id}"

@bp.route('/<int:id>/documents')
@login_required
@any_permission_required(['manage_employee_documents', 'view_employees'])
def documents(id):
    """عرض مستندات الموظف"""
    try:
        models = get_models()
        Employee = models.get('Employee')
        EmployeeDocument = models.get('EmployeeDocument')
        db = get_db()

        if not all([Employee, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('employees.index'))

        employee = Employee.query.get_or_404(id)

        # إذا لم يكن نموذج المستندات متاحاً، أعرض صفحة بسيطة
        if not EmployeeDocument:
            return render('employees/documents_simple.html', employee=employee)

        # جلب المستندات مع الفلترة
        page = request.GET.get('page', 1, type=int)
        document_type = request.GET.get('type', '')
        search = request.GET.get('search', '')

        query = EmployeeDocument.query.filter_by(employee_id=id, is_active=True)

        if document_type:
            query = query.filter(EmployeeDocument.document_type == document_type)

        if search:
            query = query.filter(
                or_(
                    EmployeeDocument.title.contains(search),
                    EmployeeDocument.original_filename.contains(search),
                    EmployeeDocument.description.contains(search)
                )
            )

        documents = query.order_by(EmployeeDocument.created_at.desc()).paginate(
            page=page, per_page=10, error_out=False
        )

        # أنواع المستندات المتاحة
        document_types = [
            ('contract', 'عقد العمل'),
            ('id_copy', 'صورة الهوية'),
            ('certificate', 'الشهادات'),
            ('medical', 'التقارير الطبية'),
            ('performance', 'تقييم الأداء'),
            ('training', 'شهادات التدريب'),
            ('other', 'أخرى')
        ]

        return render('employees/documents.html',
                             employee=employee,
                             documents=documents,
                             document_types=document_types,
                             selected_type=document_type,
                             search=search,
                             today=date.today(),
                             # متغيرات رأس التقرير
                             report_title=f"تقرير مستندات الموظف: {employee.full_name}",
                             report_date=datetime.now().strftime('%Y-%m-%d'))
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('employees.view', id=id))

@bp.route('/<int:id>/documents/upload', methods=['POST'])
@login_required
@permission_required('manage_employee_documents')
def upload_document(id):
    """رفع مستند جديد للموظف"""
    try:
        models = get_models()
        Employee = models.get('Employee')
        EmployeeDocument = models.get('EmployeeDocument')
        db = get_db()

        if not all([Employee, EmployeeDocument, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        employee = Employee.query.get_or_404(id)

        # التحقق من وجود ملف
        if 'file' not in request.files:
            return JsonResponse({'success': False, 'message': 'لم يتم اختيار ملف'})

        file = request.files['file']
        if file.filename == '':
            return JsonResponse({'success': False, 'message': 'لم يتم اختيار ملف'})

        # التحقق من نوع الملف
        if not allowed_file(file.filename):
            return JsonResponse({'success': False, 'message': 'نوع الملف غير مدعوم'})

        # التحقق من حجم الملف (50 ميجابايت)
        file.seek(0, 2)  # الانتقال لنهاية الملف
        file_size = file.tell()
        file.seek(0)  # العودة لبداية الملف

        if file_size > 50 * 1024 * 1024:  # 50 MB
            return JsonResponse({'success': False, 'message': 'حجم الملف كبير جداً (الحد الأقصى 50 ميجابايت)'})

        # إنشاء مجلد الموظف
        employee_folder = os.path.join(ensure_upload_folder(), str(employee.id))
        if not os.path.exists(employee_folder):
            os.makedirs(employee_folder)

        # إنشاء اسم ملف فريد
        original_filename = secure_filename(file.filename)
        file_extension = original_filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
        file_path = os.path.join(employee_folder, unique_filename)

        # حفظ الملف
        file.save(file_path)

        # جلب بيانات النموذج
        document_type = request.POST.get('document_type', 'other')
        title = request.POST.get('title', '').strip()
        description = request.POST.get('description', '').strip()
        document_date = request.POST.get('document_date')
        expiry_date = request.POST.get('expiry_date')
        is_confidential = request.POST.get('is_confidential') == 'on'

        # تحويل التواريخ
        document_date_obj = datetime.strptime(document_date, '%Y-%m-%d').date() if document_date else None
        expiry_date_obj = datetime.strptime(expiry_date, '%Y-%m-%d').date() if expiry_date else None

        # إنشاء سجل المستند
        document = EmployeeDocument(
            employee_id=id,
            filename=unique_filename,
            original_filename=original_filename,
            file_path=file_path,
            file_size=file_size,
            file_type=file_extension,
            document_type=document_type,
            title=title if title else original_filename,
            description=description,
            document_date=document_date_obj,
            expiry_date=expiry_date_obj,
            is_confidential=is_confidential,
            uploaded_by=current_user.id
        )

        db.add(document)
        db.save()

        return JsonResponse({'success': True, 'message': 'تم رفع المستند بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/documents/<int:doc_id>/download')
@login_required
@permission_required('manage_employee_documents')
def download_document(doc_id):
    """تحميل مستند الموظف"""
    try:
        models = get_models()
        EmployeeDocument = models.get('EmployeeDocument')

        if not EmployeeDocument:
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('employees.index'))

        document = EmployeeDocument.query.get_or_404(doc_id)

        # التحقق من وجود الملف
        if not os.path.exists(document.file_path):
            flash('الملف غير موجود', 'error')
            return redirect(url_for('employees.documents', id=document.employee_id))

        return send_file(
            document.file_path,
            as_attachment=True,
            download_name=document.original_filename
        )
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('employees.index'))

@bp.route('/documents/<int:doc_id>/delete', methods=['POST'])
@login_required
@permission_required('manage_employee_documents')
def delete_document(doc_id):
    """حذف مستند الموظف"""
    try:
        models = get_models()
        EmployeeDocument = models.get('EmployeeDocument')
        db = get_db()

        if not all([EmployeeDocument, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        document = EmployeeDocument.query.get_or_404(doc_id)
        employee_id = document.employee_id

        # حذف الملف من النظام
        if os.path.exists(document.file_path):
            os.remove(document.file_path)

        # حذف السجل من قاعدة البيانات
        db.delete(document)
        db.save()

        return JsonResponse({'success': True, 'message': 'تم حذف المستند بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/salary-report')
@login_required
@permission_required('view_employees')
def salary_report():
    """تقرير رواتب جميع الموظفين"""
    try:
        models = get_models()
        Employee = models.get('Employee')
        Department = models.get('Department')

        if not all([Employee, Department]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # جلب جميع الموظفين النشطين
        employees = Employee.query.filter_by(is_active=True).all()

        # حساب الإحصائيات
        total_employees = len(employees)
        total_basic_salary = sum(emp.basic_salary or 0 for emp in employees)
        total_allowances = sum(emp.allowances or 0 for emp in employees)
        total_deductions = sum(emp.deductions or 0 for emp in employees)
        total_net_salary = sum(emp.total_salary or 0 for emp in employees)

        stats = {
            'total_employees': total_employees,
            'total_basic_salary': total_basic_salary,
            'total_allowances': total_allowances,
            'total_deductions': total_deductions,
            'total_net_salary': total_net_salary,
            'average_salary': total_net_salary / total_employees if total_employees > 0 else 0
        }

        return render('employees/salary_report.html',
                             employees=employees,
                             stats=stats,
                             # متغيرات رأس التقرير
                             report_title="تقرير رواتب جميع الموظفين",
                             report_period=f"الشهر الحالي - {datetime.now().strftime('%Y-%m')}",
                             report_date=datetime.now().strftime('%Y-%m-%d'),
                             # متغيرات إضافية لرأس التقرير
                             show_logo='true',
                             show_company_info='true',
                             show_print_time='true',
                             logo_path='images/logo.svg',
                             company_name='مكتب الشؤون القانونية',
                             company_subtitle='نظام إدارة الموارد البشرية والشؤون القانونية')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('employees.index'))

@bp.route('/<int:employee_id>/salary-report')
@login_required
@permission_required('view_employees')
def employee_salary_report(employee_id):
    """تقرير راتب موظف محدد"""
    try:
        models = get_models()
        Employee = models.get('Employee')

        if not Employee:
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        employee = Employee.query.get_or_404(employee_id)

        return render('employees/salary_report.html',
                             employee=employee,
                             # متغيرات رأس التقرير
                             report_title=f"تقرير راتب الموظف: {employee.full_name}",
                             report_period=f"الشهر الحالي - {datetime.now().strftime('%Y-%m')}",
                             report_date=datetime.now().strftime('%Y-%m-%d'),
                             # متغيرات إضافية لرأس التقرير
                             show_logo='true',
                             show_company_info='true',
                             show_print_time='true',
                             logo_path='images/logo.svg',
                             company_name='مكتب الشؤون القانونية',
                             company_subtitle='نظام إدارة الموارد البشرية والشؤون القانونية')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('employees.index'))

@bp.route('/export')
@login_required
@permission_required('view_employees')
def export():
    """تصدير بيانات الموظفين"""
    try:
        from django.shortcuts import make_response
        import csv
        import io

        models = get_models()
        Employee = models.get('Employee')
        Department = models.get('Department')

        if not Employee:
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('employees.index'))

        # جلب جميع الموظفين
        employees = Employee.query.all()

        # إنشاء ملف CSV
        output = io.StringIO()
        writer = csv.writer(output)

        # كتابة العناوين
        writer.writerow([
            'رقم الموظف', 'الاسم الكامل', 'البريد الإلكتروني', 'الهاتف',
            'المنصب', 'القسم', 'الراتب الأساسي', 'تاريخ التوظيف', 'الحالة'
        ])

        # كتابة بيانات الموظفين
        for employee in employees:
            writer.writerow([
                employee.employee_number or '',
                employee.full_name or '',
                employee.email or '',
                employee.phone or '',
                employee.position or '',
                employee.department.name if employee.department else '',
                employee.basic_salary or 0,
                employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '',
                'نشط' if employee.is_active else 'غير نشط'
            ])

        # إنشاء الاستجابة
        output.seek(0)
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = 'attachment; filename=employees.csv'

        return response

    except Exception as e:
        flash(f'خطأ في تصدير البيانات: {str(e)}', 'error')
        return redirect(url_for('employees.index'))
