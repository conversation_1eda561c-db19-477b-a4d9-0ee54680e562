{% extends "base.html" %} {% block title %}لوحة التحكم - نظام الشؤون القانونية{%
endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2">
    <i class="fas fa-tachometer-alt me-2"></i>
    لوحة التحكم
  </h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
      <button type="button" class="btn btn-sm btn-outline-secondary">
        <i class="fas fa-download me-1"></i>
        تصدير
      </button>
    </div>
  </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
  <!-- إحصائيات القضايا -->
  <div class="col-xl-3 col-md-6 mb-4">
    <div class="stats-card">
      <div class="d-flex align-items-center">
        <div class="flex-grow-1">
          <div class="stats-number">{{ total_cases }}</div>
          <div class="stats-label">إجمالي القضايا</div>
          <div class="stats-sublabel">
            <small class="text-success">{{ active_cases }} نشطة</small> •
            <small class="text-warning">{{ pending_cases }} معلقة</small>
          </div>
        </div>
        <div class="stats-icon">
          <i class="fas fa-folder-open fa-2x"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات العملاء -->
  <div class="col-xl-3 col-md-6 mb-4">
    <div
      class="stats-card"
      style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%)"
    >
      <div class="d-flex align-items-center">
        <div class="flex-grow-1">
          <div class="stats-number">{{ total_clients }}</div>
          <div class="stats-label">إجمالي العملاء</div>
          <div class="stats-sublabel">
            <small>{{ individual_clients }} أفراد</small> •
            <small>{{ company_clients }} شركات</small>
          </div>
        </div>
        <div class="stats-icon">
          <i class="fas fa-users fa-2x"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات الجلسات -->
  <div class="col-xl-3 col-md-6 mb-4">
    <div
      class="stats-card"
      style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)"
    >
      <div class="d-flex align-items-center">
        <div class="flex-grow-1">
          <div class="stats-number">{{ upcoming_sessions }}</div>
          <div class="stats-label">الجلسات القادمة</div>
          <div class="stats-sublabel">
            <small class="text-info">{{ today_sessions }} اليوم</small>
          </div>
        </div>
        <div class="stats-icon">
          <i class="fas fa-calendar-alt fa-2x"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات المواعيد -->
  <div class="col-xl-3 col-md-6 mb-4">
    <div
      class="stats-card"
      style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%)"
    >
      <div class="d-flex align-items-center">
        <div class="flex-grow-1">
          <div class="stats-number">{{ upcoming_appointments }}</div>
          <div class="stats-label">المواعيد القادمة</div>
          <div class="stats-sublabel">
            <small class="text-info">{{ today_appointments }} اليوم</small>
          </div>
        </div>
        <div class="stats-icon">
          <i class="fas fa-clock fa-2x"></i>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- إحصائيات إضافية -->
<div class="row mb-4">
  <!-- إحصائيات الفواتير -->
  <div class="col-xl-3 col-md-6 mb-4">
    <div
      class="stats-card"
      style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%)"
    >
      <div class="d-flex align-items-center">
        <div class="flex-grow-1">
          <div class="stats-number">{{ total_invoices }}</div>
          <div class="stats-label">إجمالي الفواتير</div>
          <div class="stats-sublabel">
            <small class="text-success"
              >{{ paid_invoices|default(0) }} مدفوعة</small
            >
            •
            <small class="text-warning"
              >{{ pending_invoices|default(0) }} معلقة</small
            >
            {% if overdue_invoices|default(0) > 0 %} •
            <small class="text-danger"
              >{{ overdue_invoices|default(0) }} متأخرة</small
            >
            {% endif %}
          </div>
        </div>
        <div class="stats-icon">
          <i class="fas fa-file-invoice-dollar fa-2x"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات الإيرادات -->
  <div class="col-xl-3 col-md-6 mb-4">
    <div
      class="stats-card"
      style="background: linear-gradient(135deg, #20c997 0%, #28a745 100%)"
    >
      <div class="d-flex align-items-center">
        <div class="flex-grow-1">
          <div class="stats-number">{{ "%.0f"|format(total_revenue) }}</div>
          <div class="stats-label">إجمالي الإيرادات</div>
          <div class="stats-sublabel">
            <small class="text-warning"
              >{{ "%.0f"|format(pending_revenue) }} معلقة</small
            >
          </div>
        </div>
        <div class="stats-icon">
          <i class="fas fa-chart-line fa-2x"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات المستندات -->
  <div class="col-xl-3 col-md-6 mb-4">
    <div
      class="stats-card"
      style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%)"
    >
      <div class="d-flex align-items-center">
        <div class="flex-grow-1">
          <div class="stats-number">{{ total_documents }}</div>
          <div class="stats-label">إجمالي المستندات</div>
        </div>
        <div class="stats-icon">
          <i class="fas fa-file-alt fa-2x"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات المحامين -->
  <div class="col-xl-3 col-md-6 mb-4">
    <div
      class="stats-card"
      style="background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%)"
    >
      <div class="d-flex align-items-center">
        <div class="flex-grow-1">
          <div class="stats-number">{{ total_lawyers }}</div>
          <div class="stats-label">إجمالي المحامين</div>
          <div class="stats-sublabel">
            <small>{{ total_users }} مستخدم نشط</small>
          </div>
        </div>
        <div class="stats-icon">
          <i class="fas fa-user-tie fa-2x"></i>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-bolt me-2"></i>
          إجراءات سريعة
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3 mb-3">
            <a href="/cases/add" class="btn btn-primary w-100">
              <i class="fas fa-plus me-2"></i>
              إضافة قضية جديدة
            </a>
          </div>
          <div class="col-md-3 mb-3">
            <a href="/clients/add" class="btn btn-success w-100">
              <i class="fas fa-user-plus me-2"></i>
              إضافة عميل جديد
            </a>
          </div>
          <div class="col-md-3 mb-3">
            <button
              onclick="alert('هذه الوحدة قيد التطوير')"
              class="btn btn-info w-100"
            >
              <i class="fas fa-calendar-plus me-2"></i>
              حجز موعد
            </button>
          </div>
          <div class="col-md-3 mb-3">
            <button
              onclick="alert('هذه الوحدة قيد التطوير')"
              class="btn btn-warning w-100"
            >
              <i class="fas fa-upload me-2"></i>
              رفع مستند
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- إحصائيات الشهر الحالي -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-chart-bar me-2"></i>
          إحصائيات الشهر الحالي
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3 text-center">
            <div class="monthly-stat">
              <div class="monthly-stat-number text-primary">
                {{ cases_this_month }}
              </div>
              <div class="monthly-stat-label">قضايا جديدة</div>
            </div>
          </div>
          <div class="col-md-3 text-center">
            <div class="monthly-stat">
              <div class="monthly-stat-number text-success">
                {{ clients_this_month }}
              </div>
              <div class="monthly-stat-label">عملاء جدد</div>
            </div>
          </div>
          <div class="col-md-3 text-center">
            <div class="monthly-stat">
              <div class="monthly-stat-number text-warning">
                {{ won_cases }}
              </div>
              <div class="monthly-stat-label">قضايا مكسوبة</div>
            </div>
          </div>
          <div class="col-md-3 text-center">
            <div class="monthly-stat">
              <div class="monthly-stat-number text-info">
                {{ closed_cases }}
              </div>
              <div class="monthly-stat-label">قضايا مغلقة</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Recent Activities and Upcoming Events -->
<div class="row">
  <!-- Recent Cases -->
  <div class="col-lg-6 mb-4">
    <div class="card">
      <div
        class="card-header d-flex justify-content-between align-items-center"
      >
        <h5 class="mb-0">
          <i class="fas fa-folder-open me-2"></i>
          أحدث القضايا
        </h5>
        <a href="/cases/" class="btn btn-sm btn-outline-primary"> عرض الكل </a>
      </div>
      <div class="card-body">
        <div class="list-group list-group-flush">
          {% if recent_cases %} {% for case in recent_cases %}
          <div
            class="list-group-item d-flex justify-content-between align-items-center"
          >
            <div>
              <h6 class="mb-1">{{ case.case_title }}</h6>
              <p class="mb-1 text-muted">{{ case.case_number }}</p>
              <small class="text-muted">
                <i class="fas fa-user me-1"></i>{{ case.client.full_name }} {%
                if case.lawyer %} • <i class="fas fa-user-tie me-1"></i>{{
                case.lawyer.user.full_name }} {% endif %}
              </small>
              <br />
              <small class="text-muted">
                <i class="fas fa-clock me-1"></i>
                {{ case.created_at|strftime("%Y-%m-%d") }}
              </small>
            </div>
            <div class="text-end">
              {% if case.case_status == 'pending' %}
              <span class="badge bg-warning">معلقة</span>
              {% elif case.case_status == 'active' %}
              <span class="badge bg-success">نشطة</span>
              {% elif case.case_status == 'closed' %}
              <span class="badge bg-secondary">مغلقة</span>
              {% elif case.case_status == 'won' %}
              <span class="badge bg-primary">مكسوبة</span>
              {% elif case.case_status == 'lost' %}
              <span class="badge bg-danger">مخسورة</span>
              {% else %}
              <span class="badge bg-light text-dark"
                >{{ case.case_status }}</span
              >
              {% endif %} {% if case.case_type %}
              <br /><small class="text-muted">{{ case.case_type }}</small>
              {% endif %}
            </div>
          </div>
          {% endfor %} {% else %}
          <div class="text-center py-4">
            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد قضايا حديثة</h5>
            <p class="text-muted">ستظهر القضايا الحديثة هنا</p>
            <a href="/cases/add" class="btn btn-primary">
              <i class="fas fa-plus me-1"></i>إضافة قضية جديدة
            </a>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Upcoming Appointments -->
  <div class="col-lg-6 mb-4">
    <div class="card">
      <div
        class="card-header d-flex justify-content-between align-items-center"
      >
        <h5 class="mb-0">
          <i class="fas fa-calendar-alt me-2"></i>
          المواعيد القادمة
        </h5>
        <button
          onclick="alert('هذه الوحدة قيد التطوير')"
          class="btn btn-sm btn-outline-primary"
        >
          عرض الكل
        </button>
      </div>
      <div class="card-body">
        <div class="list-group list-group-flush">
          <!-- This would be populated with upcoming appointments from the database -->
          <div
            class="list-group-item d-flex justify-content-between align-items-center"
          >
            <div>
              <h6 class="mb-1">استشارة قانونية - محمد علي</h6>
              <p class="mb-1 text-muted">
                <i class="fas fa-clock me-1"></i>
                اليوم 2:00 م
              </p>
              <small class="text-muted">المحامي: أحمد السيد</small>
            </div>
            <span class="badge bg-info">مجدولة</span>
          </div>
          <div
            class="list-group-item d-flex justify-content-between align-items-center"
          >
            <div>
              <h6 class="mb-1">جلسة محكمة - قضية ABC</h6>
              <p class="mb-1 text-muted">
                <i class="fas fa-clock me-1"></i>
                غداً 10:00 ص
              </p>
              <small class="text-muted">المحكمة الابتدائية</small>
            </div>
            <span class="badge bg-warning">مهمة</span>
          </div>
          <div
            class="list-group-item d-flex justify-content-between align-items-center"
          >
            <div>
              <h6 class="mb-1">اجتماع عميل - شركة XYZ</h6>
              <p class="mb-1 text-muted">
                <i class="fas fa-clock me-1"></i>
                الأحد 3:30 م
              </p>
              <small class="text-muted">المحامي: فاطمة أحمد</small>
            </div>
            <span class="badge bg-info">مجدولة</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Charts Row -->
<div class="row">
  <div class="col-lg-8 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-chart-line me-2"></i>
          إحصائيات القضايا الشهرية
        </h5>
      </div>
      <div class="card-body">
        <canvas id="casesChart" width="400" height="200"></canvas>
      </div>
    </div>
  </div>

  <div class="col-lg-4 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-chart-pie me-2"></i>
          توزيع حالات القضايا
        </h5>
      </div>
      <div class="card-body">
        <canvas id="statusChart" width="400" height="400"></canvas>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_css %}
<style>
  .stats-sublabel {
    margin-top: 5px;
    font-size: 0.85em;
  }

  .monthly-stat {
    padding: 20px 10px;
  }

  .monthly-stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
  }

  .monthly-stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
  }

  .stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
  }

  .stats-label {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
  }

  .stats-icon {
    opacity: 0.8;
  }

  .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  }

  .list-group-item {
    border: none;
    border-bottom: 1px solid #f0f0f0;
    padding: 15px 20px;
  }

  .list-group-item:last-child {
    border-bottom: none;
  }

  .badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
  }

  .monthly-stat:hover .monthly-stat-number {
    transform: scale(1.1);
    transition: transform 0.3s ease;
  }
</style>
{% endblock %} {% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script id="dashboard-data" type="application/json">
  {
    "pending_cases": {{ pending_cases|default(0)|int }},
    "active_cases": {{ active_cases|default(0)|int }},
    "closed_cases": {{ closed_cases|default(0)|int }},
    "won_cases": {{ won_cases|default(0)|int }}
  }
</script>
<script>
  // Get dashboard data
  const dashboardData = JSON.parse(
    document.getElementById("dashboard-data").textContent
  );

  // Cases Chart
  const casesCtx = document.getElementById("casesChart").getContext("2d");
  const casesChart = new Chart(casesCtx, {
    type: "line",
    data: {
      labels: ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو"],
      datasets: [
        {
          label: "القضايا الجديدة",
          data: [12, 19, 3, 5, 2, 3],
          borderColor: "#667eea",
          backgroundColor: "rgba(102, 126, 234, 0.1)",
          tension: 0.4,
        },
      ],
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          display: false,
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    },
  });

  // Status Chart
  const statusCtx = document.getElementById("statusChart").getContext("2d");
  const statusChart = new Chart(statusCtx, {
    type: "doughnut",
    data: {
      labels: ["معلقة", "نشطة", "مغلقة", "مكسوبة"],
      datasets: [
        {
          data: [
            dashboardData.pending_cases,
            dashboardData.active_cases,
            dashboardData.closed_cases,
            dashboardData.won_cases,
          ],
          backgroundColor: ["#ffc107", "#28a745", "#6c757d", "#007bff"],
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: "bottom",
          labels: {
            padding: 20,
            usePointStyle: true,
          },
        },
      },
    },
  });
</script>
{% endblock %}
