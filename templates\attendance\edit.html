{% extends "base.html" %}

{% block title %}تعديل سجل حضور{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-edit"></i>
            تعديل سجل حضور - {{ attendance.employee.full_name }}
          </h3>
          <div class="card-tools">
            <a href="{{ {% url 'attendance_index' %} }}" class="btn btn-secondary btn-sm">
              <i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a>
          </div>
        </div>

        <form id="attendanceForm">
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="employee_id">الموظف <span class="text-danger">*</span></label>
                  <select class="form-control" id="employee_id" name="employee_id" required>
                    <option value="">اختر الموظف</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}" 
                            {% if attendance.employee_id == employee.id %}selected{% endif %}>
                      {{ employee.full_name }} - {{ employee.employee_number }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="date">التاريخ <span class="text-danger">*</span></label>
                  <input type="date" class="form-control" id="date" name="date" 
                         value="{{ attendance.date|strftime("%Y-%m-%d") }}" required>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="check_in">وقت الحضور</label>
                  <input type="time" class="form-control" id="check_in" name="check_in"
                         value="{% if attendance.check_in %}{{ attendance.check_in|strftime("%H:%M") }}{% endif %}">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="check_out">وقت الانصراف</label>
                  <input type="time" class="form-control" id="check_out" name="check_out"
                         value="{% if attendance.check_out %}{{ attendance.check_out|strftime("%H:%M") }}{% endif %}">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="break_start">بداية الاستراحة</label>
                  <input type="time" class="form-control" id="break_start" name="break_start"
                         value="{% if attendance.break_start %}{{ attendance.break_start|strftime("%H:%M") }}{% endif %}">
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="break_end">نهاية الاستراحة</label>
                  <input type="time" class="form-control" id="break_end" name="break_end"
                         value="{% if attendance.break_end %}{{ attendance.break_end|strftime("%H:%M") }}{% endif %}">
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="status">الحالة</label>
                  <select class="form-control" id="status" name="status">
                    <option value="present" {% if attendance.status == 'present' %}selected{% endif %}>حاضر</option>
                    <option value="absent" {% if attendance.status == 'absent' %}selected{% endif %}>غائب</option>
                    <option value="late" {% if attendance.status == 'late' %}selected{% endif %}>متأخر</option>
                    <option value="half_day" {% if attendance.status == 'half_day' %}selected{% endif %}>نصف يوم</option>
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="location">الموقع</label>
                  <input type="text" class="form-control" id="location" name="location" 
                         value="{{ attendance.location or '' }}" placeholder="مكتب، عمل ميداني، إلخ...">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="hours_worked">ساعات العمل</label>
                  <input type="number" class="form-control" id="hours_worked" name="hours_worked" 
                         step="0.25" min="0" max="24" readonly
                         value="{% if attendance.hours_worked %}{{ '%.2f'|format(attendance.hours_worked) }}{% endif %}">
                  <small class="form-text text-muted">يتم حسابها تلقائياً</small>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="notes">ملاحظات</label>
                  <textarea class="form-control" id="notes" name="notes" rows="3" 
                            placeholder="أي ملاحظات إضافية...">{{ attendance.notes or '' }}</textarea>
                </div>
              </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="row">
              <div class="col-12">
                <div class="card card-outline card-info">
                  <div class="card-header">
                    <h3 class="card-title">معلومات السجل</h3>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-4">
                        <strong>تاريخ الإنشاء:</strong><br>
                        <small class="text-muted">{{ attendance.created_at|strftime("%Y-%m-%d %H:%M") }}</small>
                      </div>
                      <div class="col-md-4">
                        <strong>منشئ السجل:</strong><br>
                        <small class="text-muted">
                          {% if attendance.creator %}{{ attendance.creator.username }}{% else %}النظام{% endif %}
                        </small>
                      </div>
                      <div class="col-md-4">
                        <strong>عنوان IP:</strong><br>
                        <small class="text-muted">{{ attendance.ip_address or '-' }}</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i>
              حفظ التعديلات
            </button>
            <a href="{{ {% url 'attendance_index' %} }}" class="btn btn-secondary">
              <i class="fas fa-times"></i>
              إلغاء
            </a>
            <button type="button" class="btn btn-danger float-right"
                    onclick="deleteAttendance('{{ attendance.id }}')">
              <i class="fas fa-trash"></i>
              حذف السجل
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
// حساب ساعات العمل تلقائياً
function calculateHours() {
  const checkIn = document.getElementById('check_in').value;
  const checkOut = document.getElementById('check_out').value;
  const breakStart = document.getElementById('break_start').value;
  const breakEnd = document.getElementById('break_end').value;
  
  if (checkIn && checkOut) {
    // تحويل الأوقات إلى دقائق
    const checkInMinutes = timeToMinutes(checkIn);
    const checkOutMinutes = timeToMinutes(checkOut);
    
    let totalMinutes = checkOutMinutes - checkInMinutes;
    
    // إذا كان وقت الانصراف في اليوم التالي
    if (totalMinutes < 0) {
      totalMinutes += 24 * 60;
    }
    
    // خصم وقت الاستراحة
    if (breakStart && breakEnd) {
      const breakStartMinutes = timeToMinutes(breakStart);
      const breakEndMinutes = timeToMinutes(breakEnd);
      let breakDuration = breakEndMinutes - breakStartMinutes;
      
      if (breakDuration < 0) {
        breakDuration += 24 * 60;
      }
      
      totalMinutes -= breakDuration;
    }
    
    // تحويل إلى ساعات
    const hours = Math.max(0, totalMinutes / 60);
    document.getElementById('hours_worked').value = hours.toFixed(2);
  }
}

function timeToMinutes(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

// ربط الأحداث
document.getElementById('check_in').addEventListener('change', calculateHours);
document.getElementById('check_out').addEventListener('change', calculateHours);
document.getElementById('break_start').addEventListener('change', calculateHours);
document.getElementById('break_end').addEventListener('change', calculateHours);

// إرسال النموذج
document.getElementById('attendanceForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  
  // إظهار مؤشر التحميل
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
  submitBtn.disabled = true;
  
  fetch('{{ url_for("attendance.edit", attendance_id=attendance.id) }}', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // إظهار رسالة نجاح
      showAlert('success', data.message);
      
      // إعادة توجيه بعد ثانيتين
      setTimeout(() => {
        window.location.href = '{% url "attendance:index" %}';
      }, 2000);
    } else {
      showAlert('danger', data.message);
      
      // إعادة تفعيل الزر
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
    }
  })
  .catch(error => {
    showAlert('danger', 'حدث خطأ في الاتصال');
    
    // إعادة تفعيل الزر
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  });
});

// حذف سجل الحضور
function deleteAttendance(id) {
  if (confirm('هل أنت متأكد من حذف سجل الحضور؟\nلا يمكن التراجع عن هذا الإجراء.')) {
    fetch(`/attendance/${id}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showAlert('success', data.message);
        setTimeout(() => {
          window.location.href = '{% url "attendance:index" %}';
        }, 2000);
      } else {
        showAlert('danger', data.message);
      }
    })
    .catch(error => {
      showAlert('danger', 'حدث خطأ في الاتصال');
    });
  }
}

function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert">
      <span>&times;</span>
    </button>
  `;
  
  // إدراج التنبيه في أعلى الصفحة
  const container = document.querySelector('.container-fluid');
  container.insertBefore(alertDiv, container.firstChild);
  
  // إزالة التنبيه بعد 5 ثوان
  setTimeout(() => {
    alertDiv.remove();
  }, 5000);
}
</script>
{% endblock %}
