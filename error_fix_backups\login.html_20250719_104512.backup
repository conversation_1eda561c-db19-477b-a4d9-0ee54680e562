<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تسجيل الدخول - نظام الشؤون القانونية</title>

    <!-- Bootstrap CSS (RTL) -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Google Fonts - Arabic -->
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      body {
        font-family: "Cairo", sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
      }

      .login-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 50px 40px;
        max-width: 450px;
        margin: 0 auto;
      }

      .login-header {
        text-align: center;
        margin-bottom: 40px;
      }

      .logo {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 20px;
      }

      .login-title {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
      }

      .login-subtitle {
        color: #666;
        font-size: 1rem;
      }

      .form-group {
        margin-bottom: 25px;
      }

      .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .form-control {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 15px 20px;
        font-size: 1rem;
        transition: all 0.3s ease;
      }

      .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
      }

      .input-group {
        position: relative;
      }

      .input-group-text {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #667eea;
        z-index: 10;
      }

      .form-control.with-icon {
        padding-right: 50px;
      }

      .btn-login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 15px;
        font-size: 1.1rem;
        font-weight: 600;
        color: white;
        width: 100%;
        transition: all 0.3s ease;
      }

      .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
      }

      .back-link {
        text-align: center;
        margin-top: 30px;
      }

      .back-link a {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
      }

      .back-link a:hover {
        color: #764ba2;
      }

      .alert {
        border: none;
        border-radius: 12px;
        margin-bottom: 25px;
      }

      .demo-info {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
        text-align: center;
      }

      .demo-info h6 {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 10px;
      }

      .demo-info p {
        margin: 5px 0;
        font-size: 0.9rem;
        color: #666;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="login-container">
        <div class="login-header">
          <div class="logo">
            <i class="fas fa-balance-scale"></i>
          </div>
          <h1 class="login-title">تسجيل الدخول</h1>
          <p class="login-subtitle">أدخل بياناتك للوصول إلى النظام</p>
        </div>

        <!-- Demo credentials info -->
        <div class="demo-info">
          <h6><i class="fas fa-info-circle me-2"></i>بيانات تجريبية</h6>
          <p><strong>اسم المستخدم:</strong> admin</p>
          <p><strong>كلمة المرور:</strong> admin123</p>
        </div>

        <!-- Flash messages -->
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %} {% for category, message in messages %}
        <div
          class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show"
          role="alert"
        >
          {{ message }}
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="alert"
          ></button>
        </div>
        {% endfor %} {% endif %} {% endwith %}

        <form method="POST">
          <div class="form-group">
            <label for="username" class="form-label">اسم المستخدم</label>
            <div class="input-group">
              <input
                type="text"
                class="form-control with-icon"
                id="username"
                name="username"
                required
              />
              <span class="input-group-text">
                <i class="fas fa-user"></i>
              </span>
            </div>
          </div>

          <div class="form-group">
            <label for="password" class="form-label">كلمة المرور</label>
            <div class="input-group">
              <input
                type="password"
                class="form-control with-icon"
                id="password"
                name="password"
                required
              />
              <span class="input-group-text">
                <i class="fas fa-lock"></i>
              </span>
            </div>
          </div>

          <button type="submit" class="btn btn-login">
            <i class="fas fa-sign-in-alt me-2"></i>
            تسجيل الدخول
          </button>
        </form>

        <div class="back-link">
          <a href="{{ url_for('index') }}">
            <i class="fas fa-arrow-right me-2"></i>
            العودة إلى الصفحة الرئيسية
          </a>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      // Auto-hide alerts after 5 seconds
      setTimeout(function () {
        const alerts = document.querySelectorAll(".alert");
        alerts.forEach(function (alert) {
          alert.style.transition = "opacity 0.5s";
          alert.style.opacity = "0";
          setTimeout(function () {
            if (alert.parentNode) {
              alert.remove();
            }
          }, 500);
        });
      }, 5000);
    </script>
  </body>
</html>
