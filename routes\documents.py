from django.shortcuts import Blueprint, render_template, request, redirect, url_for, flash, send_file, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime
import os

bp = Blueprint('documents', __name__, url_prefix='/documents')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from django.shortcuts import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        # في حالة عدم وجود app context
        return {}

ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@bp.route('/')
@login_required
def index():
    """عرض جميع المستندات"""
    try:
        return render('documents/index.html')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    """رفع مستند جديد"""
    if request.method == 'POST':
        try:
            if 'file' not in request.files:
                flash('لم يتم اختيار ملف', 'error')
                return redirect(request.url)
            
            file = request.files['file']
            
            if file.filename == '':
                flash('لم يتم اختيار ملف', 'error')
                return redirect(request.url)
            
            if file and allowed_file(file.filename):
                # إنشاء اسم ملف فريد
                filename = secure_filename(file.filename)
                file_extension = filename.rsplit('.', 1)[1].lower()
                unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
                
                # إنشاء مجلد للقضية إذا لم يكن موجوداً
                case_id = request.POST['case_id']
                case_folder = os.path.join('uploads', f'case_{case_id}')
                if not os.path.exists(case_folder):
                    os.makedirs(case_folder)
                
                file_path = os.path.join(case_folder, unique_filename)
                file.save(file_path)
                
                # حفظ معلومات المستند في قاعدة البيانات
                document = Document(
                    case_id=case_id,
                    document_name=request.POST.get('document_name', filename),
                    document_type=request.POST['document_type'],
                    file_path=file_path,
                    file_size=str(os.path.getsize(file_path)),
                    mime_type=file.mimetype,
                    description=request.POST.get('description'),
                    uploaded_by=current_user.id
                )
                
                db.add(document)
                db.save()
                
                flash('تم رفع المستند بنجاح', 'success')
                return redirect(url_for('documents.view', id=document.id))
            else:
                flash('نوع الملف غير مسموح', 'error')
                
        except Exception as e:
            db.delete()
            flash(f'حدث خطأ أثناء رفع المستند: {str(e)}', 'error')
    
    cases = Case.query.filter_by(is_active=True).all()
    return render('documents/upload.html', cases=cases)

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل المستند"""
    document = Document.query.get_or_404(id)
    return render('documents/view.html', document=document)

@bp.route('/<int:id>/download')
@login_required
def download(id):
    """تحميل المستند"""
    document = Document.query.get_or_404(id)
    
    try:
        return send_file(document.file_path, as_attachment=True, 
                        download_name=document.document_name)
    except Exception as e:
        flash(f'حدث خطأ أثناء تحميل المستند: {str(e)}', 'error')
        return redirect(url_for('documents.view', id=id))

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل معلومات المستند"""
    document = Document.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            document.document_name = request.POST['document_name']
            document.document_type = request.POST['document_type']
            document.description = request.POST.get('description')
            
            db.save()
            
            flash('تم تحديث المستند بنجاح', 'success')
            return redirect(url_for('documents.view', id=document.id))
            
        except Exception as e:
            db.delete()
            flash(f'حدث خطأ أثناء تحديث المستند: {str(e)}', 'error')
    
    return render('documents/edit.html', document=document)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف المستند"""
    document = Document.query.get_or_404(id)
    
    try:
        # حذف الملف من النظام
        if os.path.exists(document.file_path):
            os.remove(document.file_path)
        
        # إلغاء تفعيل المستند في قاعدة البيانات
        document.is_active = False
        db.save()
        
        flash('تم حذف المستند بنجاح', 'success')
        
    except Exception as e:
        db.delete()
        flash(f'حدث خطأ أثناء حذف المستند: {str(e)}', 'error')
    
    return redirect(url_for('documents.index'))

@bp.route('/case/<int:case_id>')
@login_required
def case_documents(case_id):
    """عرض مستندات قضية معينة"""
    case = Case.query.get_or_404(case_id)
    documents = Document.query.filter_by(case_id=case_id, is_active=True).order_by(Document.upload_date.desc()).all()
    
    return render('documents/case_documents.html', case=case, documents=documents)
